<?php
include 'includes/header.php';

// Check if user is logged in as doctor
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'doctor') {
  header("Location: doctor_login.php");
  exit;
}

$doctor_id = $_SESSION['user_id'];
$doctor_name = $_SESSION['doctor_name'];

// Get current week's appointments
$start_of_week = date('Y-m-d', strtotime('monday this week'));
$end_of_week = date('Y-m-d', strtotime('sunday this week'));

$weekly_appointments = $conn->prepare("
  SELECT a.appointment_id, p.full_name as patient_name, 
         a.appointment_date, a.consultation_type,
         DATE(a.appointment_date) as appointment_day,
         TIME(a.appointment_date) as appointment_time
  FROM appointments a
  JOIN patients p ON a.patient_id = p.patient_id
  WHERE a.doctor_id = ? 
    AND DATE(a.appointment_date) BETWEEN ? AND ?
  ORDER BY a.appointment_date ASC
");
$weekly_appointments->bind_param('iss', $doctor_id, $start_of_week, $end_of_week);
$weekly_appointments->execute();
$weekly_result = $weekly_appointments->get_result();

// Organize appointments by day
$schedule = [];
$days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
foreach ($days as $day) {
  $schedule[$day] = [];
}

while ($apt = $weekly_result->fetch_assoc()) {
  $day_name = date('l', strtotime($apt['appointment_day']));
  $schedule[$day_name][] = $apt;
}

// Get next week's date range for navigation
$next_week_start = date('Y-m-d', strtotime('monday next week'));
$prev_week_start = date('Y-m-d', strtotime('monday last week'));
?>

<div class="dashboard">
  <h2>🗓️ My Schedule</h2>
  <p style="color: #666; margin-bottom: 2rem;">
    Dr. <?= htmlspecialchars($doctor_name) ?> - Weekly Schedule
  </p>

  <!-- Week Navigation -->
  <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 10px; margin-bottom: 2rem; display: flex; justify-content: space-between; align-items: center;">
    <a href="?week=<?= $prev_week_start ?>" style="background: #6c757d; color: white; padding: 0.5rem 1rem; border-radius: 5px; text-decoration: none;">
      ← Previous Week
    </a>
    
    <h3 style="margin: 0;">
      Week of <?= date('M j', strtotime($start_of_week)) ?> - <?= date('M j, Y', strtotime($end_of_week)) ?>
    </h3>
    
    <a href="?week=<?= $next_week_start ?>" style="background: #007bff; color: white; padding: 0.5rem 1rem; border-radius: 5px; text-decoration: none;">
      Next Week →
    </a>
  </div>

  <!-- Quick Actions -->
  <div style="margin-bottom: 2rem;">
    <a href="doctor_appointments.php" class="btn-primary" style="margin-right: 1rem;">📅 All Appointments</a>
    <a href="doctor_appointments.php?filter_date=<?= date('Y-m-d') ?>" class="btn-primary" style="margin-right: 1rem;">📋 Today's Appointments</a>
    <a href="doctor_dashboard.php" style="background: #6c757d; color: white; padding: 0.75rem 1rem; border-radius: 5px; text-decoration: none;">← Back to Dashboard</a>
  </div>

  <!-- Weekly Schedule Grid -->
  <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
    <?php foreach ($days as $day): ?>
      <?php 
      $day_date = date('Y-m-d', strtotime($day . ' this week', strtotime($start_of_week)));
      $is_today = $day_date === date('Y-m-d');
      $day_appointments = $schedule[$day];
      ?>
      
      <div style="background: <?= $is_today ? '#fff3cd' : 'white' ?>; border: <?= $is_today ? '2px solid #ffc107' : '1px solid #e1e5e9' ?>; border-radius: 10px; padding: 1rem;">
        <h4 style="margin-bottom: 1rem; color: #2c5aa0; display: flex; justify-content: space-between; align-items: center;">
          <?= $day ?>
          <small style="color: #666; font-weight: normal;">
            <?= date('M j', strtotime($day_date)) ?>
            <?php if ($is_today): ?>
              <span style="background: #ffc107; color: black; padding: 0.2rem 0.4rem; border-radius: 3px; font-size: 0.7rem; margin-left: 0.5rem;">TODAY</span>
            <?php endif; ?>
          </small>
        </h4>
        
        <?php if (empty($day_appointments)): ?>
          <p style="color: #666; text-align: center; padding: 2rem 0; font-style: italic;">
            No appointments scheduled
          </p>
        <?php else: ?>
          <?php foreach ($day_appointments as $apt): ?>
            <div style="background: #f8f9fa; padding: 0.75rem; border-radius: 5px; margin-bottom: 0.5rem; border-left: 4px solid #2c5aa0;">
              <div style="font-weight: 600; color: #2c5aa0;">
                <?= date('g:i A', strtotime($apt['appointment_time'])) ?>
              </div>
              <div style="margin: 0.25rem 0;">
                <?= htmlspecialchars($apt['patient_name']) ?>
              </div>
              <div style="font-size: 0.8rem; color: #666;">
                <?= htmlspecialchars($apt['consultation_type']) ?>
              </div>
            </div>
          <?php endforeach; ?>
        <?php endif; ?>
      </div>
    <?php endforeach; ?>
  </div>

  <!-- Schedule Summary -->
  <div style="margin-top: 3rem; background: #f8f9fa; padding: 1.5rem; border-radius: 10px;">
    <h3>This Week's Summary</h3>
    <?php
    $total_this_week = 0;
    $busiest_day = '';
    $max_appointments = 0;
    
    foreach ($schedule as $day => $appointments) {
      $count = count($appointments);
      $total_this_week += $count;
      if ($count > $max_appointments) {
        $max_appointments = $count;
        $busiest_day = $day;
      }
    }
    ?>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">📅</div>
        <strong><?= $total_this_week ?></strong><br>
        <small>Total Appointments</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">📊</div>
        <strong><?= $busiest_day ?: 'None' ?></strong><br>
        <small>Busiest Day (<?= $max_appointments ?> appointments)</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">⏰</div>
        <strong><?= $total_this_week > 0 ? round($total_this_week / 7, 1) : 0 ?></strong><br>
        <small>Average per Day</small>
      </div>
    </div>
  </div>
</div>

<?php include 'includes/footer.php'; ?>

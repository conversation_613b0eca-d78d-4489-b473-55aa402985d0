-- Add missing tables to work with existing schema

-- Create patient logins table (for authentication) - Updated to match your structure
CREATE TABLE IF NOT EXISTS patient_logins (
    login_id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE
);

-- Add loyalty status column to patients table if it doesn't exist
ALTER TABLE patients ADD COLUMN IF NOT EXISTS loyalty_status ENUM('New', 'Loyal') DEFAULT 'New';

-- Insert some sample doctors if the table is empty
INSERT IGNORE INTO doctors (full_name, specialty) VALUES
('Dr. <PERSON>', 'General Medicine'),
('Dr. <PERSON>', 'Cardiology'),
('Dr. <PERSON><PERSON>', 'Dermatology'),
('Dr. <PERSON>', 'Pediatrics');

-- Insert some sample products if the table is empty
INSERT IGNORE INTO products (product_name, category, price, stock, description) VALUES
('Paracetamol 500mg', 'Medicine', 150.00, 100, 'Pain relief and fever reducer'),
('Vitamin C Tablets', 'Supplements', 250.00, 50, 'Immune system support'),
('Digital Thermometer', 'Medical Devices', 1200.00, 25, 'Accurate temperature measurement'),
('Hand Sanitizer 250ml', 'Hygiene', 300.00, 75, 'Antibacterial hand sanitizer'),
('Face Masks (Pack of 10)', 'Protection', 500.00, 40, 'Disposable surgical masks');

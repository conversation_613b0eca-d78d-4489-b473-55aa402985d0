<?php
include 'includes/header.php';

// Check if user is logged in as doctor
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'doctor') {
  header("Location: doctor_login.php");
  exit;
}

$doctor_id = $_SESSION['user_id'];
$doctor_name = $_SESSION['doctor_name'];

// Get patients who have had appointments with this doctor
$patients = $conn->prepare("
  SELECT DISTINCT p.patient_id, p.full_name, p.email, p.phone, p.dob,
         COUNT(a.appointment_id) as appointment_count,
         MAX(a.appointment_date) as last_appointment,
         TIMESTAMPDIFF(YEAR, p.dob, CURDATE()) as age
  FROM patients p
  JOIN appointments a ON p.patient_id = a.patient_id
  WHERE a.doctor_id = ?
  GROUP BY p.patient_id
  ORDER BY last_appointment DESC
");
$patients->bind_param('i', $doctor_id);
$patients->execute();
$patients_result = $patients->get_result();

// Get total unique patients count
$total_patients = $patients_result->num_rows;
?>

<div class="dashboard">
  <h2>👥 My Patients</h2>
  <p style="color: #666; margin-bottom: 2rem;">
    Dr. <?= htmlspecialchars($doctor_name) ?> - Patients you have treated
  </p>

  <!-- Quick Stats -->
  <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 10px; margin-bottom: 2rem;">
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">👥</div>
        <strong><?= $total_patients ?></strong><br>
        <small>Total Patients</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">📊</div>
        <strong><?= htmlspecialchars($_SESSION['doctor_specialty']) ?></strong><br>
        <small>Your Specialty</small>
      </div>
    </div>
  </div>

  <!-- Navigation -->
  <div style="margin-bottom: 2rem;">
    <a href="doctor_appointments.php" class="btn-primary" style="margin-right: 1rem;">📅 View Appointments</a>
    <a href="doctor_dashboard.php" style="background: #6c757d; color: white; padding: 0.75rem 1rem; border-radius: 5px; text-decoration: none;">← Back to Dashboard</a>
  </div>

  <!-- Patients List -->
  <div>
    <h3>Patient List</h3>
    <?php if ($patients_result && $patients_result->num_rows > 0): ?>
      <table>
        <thead>
          <tr>
            <th>Patient Name</th>
            <th>Age</th>
            <th>Contact</th>
            <th>Appointments</th>
            <th>Last Visit</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <?php 
          $patients_result->data_seek(0); // Reset pointer
          while($patient = $patients_result->fetch_assoc()): 
          ?>
            <tr>
              <td>
                <strong><?= htmlspecialchars($patient['full_name']) ?></strong><br>
                <small>DOB: <?= date('M j, Y', strtotime($patient['dob'])) ?></small>
              </td>
              <td><?= $patient['age'] ?> years</td>
              <td>
                <small>
                  📧 <?= htmlspecialchars($patient['email']) ?><br>
                  📞 <?= htmlspecialchars($patient['phone']) ?>
                </small>
              </td>
              <td>
                <span style="background: #007bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                  <?= $patient['appointment_count'] ?> visits
                </span>
              </td>
              <td>
                <?php if ($patient['last_appointment']): ?>
                  <?= date('M j, Y', strtotime($patient['last_appointment'])) ?>
                <?php else: ?>
                  <span style="color: #666;">No visits</span>
                <?php endif; ?>
              </td>
              <td>
                <a href="doctor_patient_history.php?patient_id=<?= $patient['patient_id'] ?>" 
                   style="background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; text-decoration: none; font-size: 0.8rem;">
                  View History
                </a>
              </td>
            </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p style="text-align: center; color: #666; padding: 2rem;">
        You haven't treated any patients yet. Patients will appear here after their first appointment with you.
      </p>
    <?php endif; ?>
  </div>

  <!-- Patient Statistics -->
  <?php if ($total_patients > 0): ?>
    <div style="margin-top: 3rem; background: #f8f9fa; padding: 1.5rem; border-radius: 10px;">
      <h3>Patient Demographics</h3>
      <?php
      // Get age group statistics
      $age_stats = $conn->prepare("
        SELECT 
          CASE 
            WHEN TIMESTAMPDIFF(YEAR, p.dob, CURDATE()) < 18 THEN 'Under 18'
            WHEN TIMESTAMPDIFF(YEAR, p.dob, CURDATE()) BETWEEN 18 AND 35 THEN '18-35'
            WHEN TIMESTAMPDIFF(YEAR, p.dob, CURDATE()) BETWEEN 36 AND 55 THEN '36-55'
            WHEN TIMESTAMPDIFF(YEAR, p.dob, CURDATE()) BETWEEN 56 AND 70 THEN '56-70'
            ELSE 'Over 70'
          END as age_group,
          COUNT(DISTINCT p.patient_id) as count
        FROM patients p
        JOIN appointments a ON p.patient_id = a.patient_id
        WHERE a.doctor_id = ?
        GROUP BY age_group
        ORDER BY count DESC
      ");
      $age_stats->bind_param('i', $doctor_id);
      $age_stats->execute();
      $age_result = $age_stats->get_result();
      ?>
      
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-top: 1rem;">
        <?php while($age_group = $age_result->fetch_assoc()): ?>
          <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
            <strong><?= $age_group['count'] ?></strong><br>
            <small><?= htmlspecialchars($age_group['age_group']) ?> years</small>
          </div>
        <?php endwhile; ?>
      </div>
    </div>
  <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>

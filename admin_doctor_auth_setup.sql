-- Ad<PERSON> and <PERSON> Authentication Setup
-- This script creates the necessary tables for admin and doctor login systems

-- Create admin_logins table
CREATE TABLE IF NOT EXISTS admin_logins (
    admin_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    role ENUM('super_admin', 'admin') DEFAULT 'admin',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME NULL
);

-- Create doctor_logins table
CREATE TABLE IF NOT EXISTS doctor_logins (
    login_id INT AUTO_INCREMENT PRIMARY KEY,
    doctor_id INT NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DAT<PERSON><PERSON>E NULL,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE CASCADE
);

-- Insert default admin account (username: admin, password: admin123)
INSERT INTO admin_logins (username, password_hash, full_name, email, role) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', '<EMAIL>', 'super_admin');

-- Create default doctor login accounts for existing doctors
-- First, let's check if doctors exist and create login accounts for them
INSERT INTO doctor_logins (doctor_id, username, password_hash)
SELECT 
    doctor_id,
    CONCAT('dr', doctor_id) as username,
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' as password_hash
FROM doctors 
WHERE doctor_id NOT IN (SELECT doctor_id FROM doctor_logins WHERE doctor_id IS NOT NULL);

-- Show created tables
SELECT 'Admin Logins Table Created' as Status;
DESCRIBE admin_logins;

SELECT 'Doctor Logins Table Created' as Status;
DESCRIBE doctor_logins;

-- Show default accounts created
SELECT 'Default Admin Account' as Info, username, full_name, email, role FROM admin_logins;
SELECT 'Doctor Login Accounts' as Info, dl.username, d.full_name, d.specialty 
FROM doctor_logins dl 
JOIN doctors d ON dl.doctor_id = d.doctor_id;

-- Default login credentials:
-- Admin: username = admin, password = admin123
-- Doctors: username = dr{doctor_id}, password = doctor123 (e.g., dr1, dr2, etc.)

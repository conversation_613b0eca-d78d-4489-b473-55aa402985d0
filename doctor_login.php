<?php include 'includes/header.php'; ?>

<div class="form-container">
  <h2>🔐 Doctor <PERSON></h2>
  <p style="text-align: center; margin-bottom: 2rem; color: #666;">
    Doctor access to MediCarePlus system
  </p>

  <?php if (isset($_GET['logout']) && $_GET['logout'] == '1'): ?>
    <div class="success">
      🎉 You have been successfully logged out.
    </div>
  <?php endif; ?>

  <form method="post" action="">
    <div style="position: relative;">
      <input name="username" placeholder="Doctor Username" required style="padding-left: 40px;">
      <span style="position: absolute; left: 12px; top: 12px; color: #666;">👤</span>
    </div>

    <div style="position: relative;">
      <input name="password" type="password" placeholder="Doctor Password" required style="padding-left: 40px;">
      <span style="position: absolute; left: 12px; top: 12px; color: #666;">🔒</span>
    </div>

    <button type="submit" class="btn-primary">🚪 Doctor Sign In</button>
  </form>

  <p style="text-align: center; margin-top: 2rem;">
    <a href="index.php" style="color: #2c5aa0; text-decoration: none;">← Back to Home</a> |
    <a href="admin_login.php" style="color: #2c5aa0; text-decoration: none;">Admin Login</a> |
    <a href="login.php" style="color: #2c5aa0; text-decoration: none;">Patient Login</a>
  </p>
</div>

<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  // Doctor login authentication
  $stmt = $conn->prepare(
    "SELECT dl.doctor_id, dl.password_hash, d.full_name, d.specialty, d.email
     FROM doctor_logins dl
     JOIN doctors d ON dl.doctor_id = d.doctor_id
     WHERE dl.username = ?"
  );

  if ($stmt === false) {
    echo "<p class='error'>Database error: " . $conn->error . "</p>";
    echo "<p class='error'>Please make sure the doctor_logins table exists. Run admin_doctor_auth_setup.sql first.</p>";
  } else {
    $stmt->bind_param('s', $_POST['username']);
    $stmt->execute();
    $stmt->bind_result($doctor_id, $password_hash, $full_name, $specialty, $email);
    
    if ($stmt->fetch() && password_verify($_POST['password'], $password_hash)) {
      // Update last login time
      $stmt->close();
      $update_login = $conn->prepare("UPDATE doctor_logins SET last_login = NOW() WHERE doctor_id = ?");
      $update_login->bind_param('i', $doctor_id);
      $update_login->execute();
      
      // Set session variables
      $_SESSION = [
        'user_type' => 'doctor',
        'user_id' => $doctor_id,
        'doctor_name' => $full_name,
        'doctor_specialty' => $specialty,
        'doctor_email' => $email
      ];
      
      header("Location: doctor_dashboard.php");
      exit;
    } else {
      $stmt->close();
      echo "<p class='error'>Invalid doctor username or password.</p>";
    }
  }
}
?>

<?php include 'includes/footer.php'; ?>

<?php
include 'includes/header.php';

// Check if user is logged in as doctor
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'doctor') {
  header("Location: doctor_login.php");
  exit;
}

$doctor_id = $_SESSION['user_id'];
$doctor_name = $_SESSION['doctor_name'];

// Filter options
$filter_date = $_GET['filter_date'] ?? '';
$filter_status = $_GET['filter_status'] ?? 'all';

// Build query based on filters
$where_conditions = ["a.doctor_id = ?"];
$params = [$doctor_id];
$param_types = 'i';

if (!empty($filter_date)) {
  $where_conditions[] = "DATE(a.appointment_date) = ?";
  $params[] = $filter_date;
  $param_types .= 's';
}

$where_clause = implode(' AND ', $where_conditions);

// Get appointments for this doctor
$query = "
  SELECT a.appointment_id, p.full_name as patient_name, p.email, p.phone, p.dob,
         a.appointment_date, a.consultation_type,
         TIMESTAMPDIFF(YEAR, p.dob, CURDATE()) as age
  FROM appointments a
  JOIN patients p ON a.patient_id = p.patient_id
  WHERE $where_clause
  ORDER BY a.appointment_date DESC
";

$stmt = $conn->prepare($query);
$stmt->bind_param($param_types, ...$params);
$stmt->execute();
$appointments = $stmt->get_result();

// Get upcoming appointments count
$upcoming_stmt = $conn->prepare("SELECT COUNT(*) as count FROM appointments WHERE doctor_id = ? AND appointment_date > NOW()");
$upcoming_stmt->bind_param('i', $doctor_id);
$upcoming_stmt->execute();
$upcoming_count = $upcoming_stmt->get_result()->fetch_assoc()['count'];

// Get today's appointments count
$today_stmt = $conn->prepare("SELECT COUNT(*) as count FROM appointments WHERE doctor_id = ? AND DATE(appointment_date) = CURDATE()");
$today_stmt->bind_param('i', $doctor_id);
$today_stmt->execute();
$today_count = $today_stmt->get_result()->fetch_assoc()['count'];
?>

<div class="dashboard">
  <h2>📅 My Appointments</h2>
  <p style="color: #666; margin-bottom: 2rem;">
    Dr. <?= htmlspecialchars($doctor_name) ?> - View all your appointments
  </p>

  <!-- Quick Stats -->
  <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 10px; margin-bottom: 2rem;">
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">📋</div>
        <strong><?= $today_count ?></strong><br>
        <small>Today's Appointments</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">⏰</div>
        <strong><?= $upcoming_count ?></strong><br>
        <small>Upcoming Appointments</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">📊</div>
        <strong><?= $appointments->num_rows ?></strong><br>
        <small>Total Shown</small>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 10px; margin-bottom: 2rem;">
    <h3>Filter Appointments</h3>
    <form method="get" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Filter by Date</label>
        <input type="date" name="filter_date" value="<?= htmlspecialchars($filter_date) ?>">
      </div>
      
      <div>
        <button type="submit" class="btn-primary">Apply Filters</button>
        <a href="doctor_appointments.php" style="background: #6c757d; color: white; padding: 0.75rem 1rem; border-radius: 5px; text-decoration: none; margin-left: 0.5rem;">Clear</a>
      </div>
    </form>
  </div>

  <!-- Quick Actions -->
  <div style="margin-bottom: 2rem;">
    <a href="?filter_date=<?= date('Y-m-d') ?>" class="btn-primary" style="margin-right: 1rem;">Today's Appointments</a>
    <a href="?filter_date=<?= date('Y-m-d', strtotime('+1 day')) ?>" class="btn-primary" style="margin-right: 1rem;">Tomorrow's Appointments</a>
    <a href="doctor_dashboard.php" style="background: #6c757d; color: white; padding: 0.75rem 1rem; border-radius: 5px; text-decoration: none;">← Back to Dashboard</a>
  </div>

  <!-- Appointments List -->
  <div>
    <h3>Appointments <?= !empty($filter_date) ? 'for ' . date('M j, Y', strtotime($filter_date)) : '' ?></h3>
    <?php if ($appointments && $appointments->num_rows > 0): ?>
      <table>
        <thead>
          <tr>
            <th>Date & Time</th>
            <th>Patient</th>
            <th>Age</th>
            <th>Contact</th>
            <th>Type</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
          <?php while($apt = $appointments->fetch_assoc()): ?>
            <?php 
            $apt_datetime = strtotime($apt['appointment_date']);
            $now = time();
            $is_past = $apt_datetime < $now;
            $is_today = date('Y-m-d', $apt_datetime) === date('Y-m-d');
            $is_upcoming = $apt_datetime > $now;
            
            $status_class = '';
            $status_text = '';
            if ($is_past) {
              $status_class = 'background: #28a745; color: white;';
              $status_text = 'Completed';
            } elseif ($is_today) {
              $status_class = 'background: #ffc107; color: black;';
              $status_text = 'Today';
            } else {
              $status_class = 'background: #007bff; color: white;';
              $status_text = 'Scheduled';
            }
            ?>
            <tr style="<?= $is_today ? 'background-color: #fff3cd;' : '' ?>">
              <td>
                <strong><?= date('M j, Y', $apt_datetime) ?></strong><br>
                <small><?= date('g:i A', $apt_datetime) ?></small>
              </td>
              <td>
                <strong><?= htmlspecialchars($apt['patient_name']) ?></strong><br>
                <small>DOB: <?= date('M j, Y', strtotime($apt['dob'])) ?></small>
              </td>
              <td><?= $apt['age'] ?> years</td>
              <td>
                <small>
                  📧 <?= htmlspecialchars($apt['email']) ?><br>
                  📞 <?= htmlspecialchars($apt['phone']) ?>
                </small>
              </td>
              <td><?= htmlspecialchars($apt['consultation_type']) ?></td>
              <td>
                <span style="padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem; <?= $status_class ?>">
                  <?= $status_text ?>
                </span>
              </td>
            </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p style="text-align: center; color: #666; padding: 2rem;">
        <?= !empty($filter_date) ? 'No appointments found for the selected date.' : 'No appointments found.' ?>
      </p>
    <?php endif; ?>
  </div>
</div>

<?php include 'includes/footer.php'; ?>

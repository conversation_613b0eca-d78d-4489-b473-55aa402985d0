<?php
session_start();

// Determine redirect based on user type
$redirect_url = "index.php";
if (isset($_SESSION['user_type'])) {
    if ($_SESSION['user_type'] === 'admin') {
        $redirect_url = "admin_login.php?logout=1";
    } elseif ($_SESSION['user_type'] === 'doctor') {
        $redirect_url = "doctor_login.php?logout=1";
    } else {
        $redirect_url = "login.php?logout=1";
    }
}

session_unset();
session_destroy();
header("Location: $redirect_url");
exit;

-- Updated MediCarePlus Database Schema
-- This script creates the new database structure with departments, enhanced tables, and invoices

-- Drop existing tables if they exist (in correct order to handle foreign keys)
DROP TABLE IF EXISTS invoices;
DROP TABLE IF EXISTS appointments;
DROP TABLE IF EXISTS doctors;
DROP TABLE IF EXISTS patients;
DROP TABLE IF EXISTS admins;
DROP TABLE IF EXISTS departments;
DROP TABLE IF EXISTS admin_logins;
DROP TABLE IF EXISTS doctor_logins;
DROP TABLE IF EXISTS patient_logins;

-- Step 1: Create 'departments' table
CREATE TABLE departments (
  department_id INT AUTO_INCREMENT PRIMARY KEY,
  department_name VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Step 2: Create 'admins' table
CREATE TABLE admins (
  admin_id INT AUTO_INCREMENT PRIMARY KEY,
  full_name VARCHA<PERSON>(100) NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  phone VARCHAR(15),
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  role ENUM('super_admin', 'admin') DEFAULT 'admin',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  last_login DATETIME NULL
);

-- Step 3: Create 'patients' table
CREATE TABLE patients (
  patient_id INT AUTO_INCREMENT PRIMARY KEY,
  full_name VARCHAR(100) NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  phone VARCHAR(15),
  dob DATE,
  gender ENUM('Male', 'Female', 'Other') DEFAULT 'Male',
  address TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Step 4: Create 'patient_logins' table (for patient authentication)
CREATE TABLE patient_logins (
  login_id INT AUTO_INCREMENT PRIMARY KEY,
  patient_id INT NOT NULL,
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  last_login DATETIME NULL,
  FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE
);

-- Step 5: Create 'doctors' table (linked to departments)
CREATE TABLE doctors (
  doctor_id INT AUTO_INCREMENT PRIMARY KEY,
  department_id INT,
  full_name VARCHAR(100) NOT NULL,
  specialty VARCHAR(100),
  email VARCHAR(100) UNIQUE,
  phone VARCHAR(15),
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  last_login DATETIME NULL,
  FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE SET NULL
);

-- Step 6: Create 'appointments' table
CREATE TABLE appointments (
  appointment_id INT AUTO_INCREMENT PRIMARY KEY,
  patient_id INT NOT NULL,
  doctor_id INT NOT NULL,
  appointment_date DATETIME NOT NULL,
  consultation_type VARCHAR(100),
  diagnosis TEXT,
  status ENUM('Scheduled', 'Completed', 'Cancelled', 'No-Show') DEFAULT 'Scheduled',
  created_by ENUM('admin', 'doctor', 'patient') DEFAULT 'admin',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE,
  FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE CASCADE
);

-- Step 7: Create 'invoices' table (linked to appointments and patients)
CREATE TABLE invoices (
  invoice_id INT AUTO_INCREMENT PRIMARY KEY,
  appointment_id INT,
  patient_id INT NOT NULL,
  invoice_date DATETIME DEFAULT CURRENT_TIMESTAMP,
  amount DECIMAL(10,2) NOT NULL,
  payment_status ENUM('Paid', 'Pending', 'Overdue') DEFAULT 'Pending',
  payment_method VARCHAR(50),
  notes TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (appointment_id) REFERENCES appointments(appointment_id) ON DELETE SET NULL,
  FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE
);

-- Insert sample departments
INSERT INTO departments (department_name, description) VALUES
('General Medicine', 'Primary healthcare and general medical consultations'),
('Cardiology', 'Heart and cardiovascular system specialists'),
('Dermatology', 'Skin, hair, and nail care specialists'),
('Pediatrics', 'Medical care for infants, children, and adolescents'),
('Orthopedics', 'Bone, joint, and musculoskeletal system care'),
('Neurology', 'Brain and nervous system specialists'),
('Gynecology', 'Women''s reproductive health specialists'),
('Dentistry', 'Oral health and dental care'),
('Psychiatry', 'Mental health and behavioral disorders'),
('Ophthalmology', 'Eye and vision care specialists'),
('ENT', 'Ear, nose, and throat specialists'),
('Endocrinology', 'Hormone and metabolic disorders');

-- Insert default admin account
INSERT INTO admins (full_name, email, phone, username, password_hash, role) VALUES 
('System Administrator', '<EMAIL>', '+***********', 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin'),
('Medical Administrator', '<EMAIL>', '+***********', 'medadmin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- Insert sample doctors with departments
INSERT INTO doctors (department_id, full_name, specialty, email, phone, username, password_hash) VALUES
(1, 'Dr. Sarah Johnson', 'General Practitioner', '<EMAIL>', '+***********', 'dr.sarah', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
(2, 'Dr. Michael Chen', 'Cardiologist', '<EMAIL>', '+***********', 'dr.michael', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
(3, 'Dr. Priya Patel', 'Dermatologist', '<EMAIL>', '+***********', 'dr.priya', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
(4, 'Dr. James Wilson', 'Pediatrician', '<EMAIL>', '+94771234572', 'dr.james', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
(5, 'Dr. Emily Rodriguez', 'Orthopedic Surgeon', '<EMAIL>', '+94771234573', 'dr.emily', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
(6, 'Dr. David Kumar', 'Neurologist', '<EMAIL>', '+94771234574', 'dr.david', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');

-- Show table structures for verification
SELECT 'Database Schema Created Successfully' as Status;
SHOW TABLES;

-- Default login credentials:
-- Admin: username = admin, password = admin123
-- Admin: username = medadmin, password = admin123
-- Doctors: username = dr.{firstname}, password = doctor123 (e.g., dr.sarah, dr.michael, etc.)
-- Patients: Register through the patient registration system

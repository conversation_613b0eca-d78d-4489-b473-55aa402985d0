<?php
// Database setup checker for MediCarePlus
require_once 'includes/data.php';

echo "<h2>MediCarePlus Database Setup Checker</h2>";

// Check if database connection is working
if ($conn->connect_error) {
    die("<p style='color: red;'>Connection failed: " . $conn->connect_error . "</p>");
}

echo "<p style='color: green;'>✓ Database connection successful</p>";

// Check if required tables exist
$required_tables = [
    'patients' => 'Main patient information',
    'patient_logins' => 'Patient authentication (may need to be created)',
    'doctors' => 'Doctor information',
    'appointments' => 'Appointment bookings',
    'products' => 'Health products',
    'orders' => 'Customer orders',
    'order_items' => 'Order line items'
];

echo "<h3>Table Status:</h3>";
$missing_tables = [];
$all_tables_exist = true;

foreach ($required_tables as $table => $description) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✓ $table - $description</p>";
    } else {
        echo "<p style='color: red;'>✗ $table - $description (MISSING)</p>";
        $missing_tables[] = $table;
        $all_tables_exist = false;
    }
}

if ($all_tables_exist) {
    echo "<h3 style='color: green;'>✓ All required tables exist!</h3>";
    
    // Check for sample data
    echo "<h3>Sample Data Check:</h3>";
    
    $doctor_count = $conn->query("SELECT COUNT(*) as count FROM doctors")->fetch_assoc()['count'];
    $product_count = $conn->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
    
    echo "<p>Doctors: $doctor_count</p>";
    echo "<p>Products: $product_count</p>";
    
    if ($doctor_count > 0 && $product_count > 0) {
        echo "<p style='color: green;'>✓ Sample data is available</p>";
        echo "<h3 style='color: green;'>🎉 Setup Complete!</h3>";
        echo "<p><a href='index.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Application</a></p>";
    } else {
        echo "<p style='color: orange;'>⚠ No sample data found. Consider running add_missing_tables.sql to add sample doctors and products.</p>";
    }
    
} else {
    echo "<h3 style='color: red;'>✗ Missing Tables Found</h3>";
    echo "<p>The following tables are missing: <strong>" . implode(', ', $missing_tables) . "</strong></p>";
    
    if (in_array('patient_logins', $missing_tables)) {
        echo "<h4>Quick Fix:</h4>";
        echo "<p>The most critical missing table is <strong>patient_logins</strong>. You can create it by running this SQL:</p>";
        echo "<textarea style='width: 100%; height: 150px; font-family: monospace;'>";
        echo "CREATE TABLE patient_logins (\n";
        echo "    login_id INT AUTO_INCREMENT PRIMARY KEY,\n";
        echo "    patient_id INT NOT NULL,\n";
        echo "    password_hash VARCHAR(255) NOT NULL,\n";
        echo "    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n";
        echo "    FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE\n";
        echo ");";
        echo "</textarea>";
        echo "<p><strong>How to run this:</strong></p>";
        echo "<ol>";
        echo "<li>Copy the SQL above</li>";
        echo "<li>Open phpMyAdmin at <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>";
        echo "<li>Select the 'medicareplus' database</li>";
        echo "<li>Go to the 'SQL' tab</li>";
        echo "<li>Paste the SQL and click 'Go'</li>";
        echo "<li>Refresh this page</li>";
        echo "</ol>";
    }
    
    echo "<h4>Complete Fix:</h4>";
    echo "<p>For a complete setup with sample data, run the <strong>add_missing_tables.sql</strong> file:</p>";
    echo "<ol>";
    echo "<li>Open phpMyAdmin at <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>";
    echo "<li>Select the 'medicareplus' database</li>";
    echo "<li>Go to the 'SQL' tab</li>";
    echo "<li>Copy and paste the contents of <strong>add_missing_tables.sql</strong></li>";
    echo "<li>Click 'Go' to execute</li>";
    echo "<li>Refresh this page to verify</li>";
    echo "</ol>";
}

// Test a simple query to verify table structure
if ($all_tables_exist) {
    echo "<h3>Table Structure Verification:</h3>";
    
    // Test patients table
    $test_query = $conn->query("DESCRIBE patients");
    if ($test_query) {
        echo "<p style='color: green;'>✓ Patients table structure is accessible</p>";
    } else {
        echo "<p style='color: red;'>✗ Issue with patients table: " . $conn->error . "</p>";
    }
    
    // Test patient_logins table
    $test_query = $conn->query("DESCRIBE patient_logins");
    if ($test_query) {
        echo "<p style='color: green;'>✓ Patient_logins table structure is accessible</p>";
    } else {
        echo "<p style='color: red;'>✗ Issue with patient_logins table: " . $conn->error . "</p>";
    }
}
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f5f5f5;
}
h2, h3, h4 { 
    color: #333; 
}
textarea { 
    background: #f8f8f8; 
    border: 1px solid #ddd; 
    padding: 10px; 
    border-radius: 4px;
}
ol, ul { 
    line-height: 1.6; 
}
a { 
    color: #007cba; 
}
</style>

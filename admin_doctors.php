<?php
include 'includes/header.php';

// Check if user is logged in as admin
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
  header("Location: admin_login.php");
  exit;
}

// Handle doctor actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    switch ($_POST['action']) {
      case 'add':
        $stmt = $conn->prepare("INSERT INTO doctors (full_name, specialty, email, phone) VALUES (?, ?, ?, ?)");
        $stmt->bind_param('ssss', $_POST['full_name'], $_POST['specialty'], $_POST['email'], $_POST['phone']);
        if ($stmt->execute()) {
          $doctor_id = $stmt->insert_id;
          
          // Create login account for the new doctor
          $username = 'dr' . $doctor_id;
          $password_hash = password_hash('doctor123', PASSWORD_BCRYPT);
          $login_stmt = $conn->prepare("INSERT INTO doctor_logins (doctor_id, username, password_hash) VALUES (?, ?, ?)");
          $login_stmt->bind_param('iss', $doctor_id, $username, $password_hash);
          $login_stmt->execute();
          
          echo "<div class='success'>Doctor added successfully! Login: $username / doctor123</div>";
        } else {
          echo "<div class='error'>Error adding doctor: " . $stmt->error . "</div>";
        }
        break;
        
      case 'edit':
        $stmt = $conn->prepare("UPDATE doctors SET full_name = ?, specialty = ?, email = ?, phone = ? WHERE doctor_id = ?");
        $stmt->bind_param('ssssi', $_POST['full_name'], $_POST['specialty'], $_POST['email'], $_POST['phone'], $_POST['doctor_id']);
        if ($stmt->execute()) {
          echo "<div class='success'>Doctor updated successfully!</div>";
        } else {
          echo "<div class='error'>Error updating doctor: " . $stmt->error . "</div>";
        }
        break;
        
      case 'delete':
        // First delete from doctor_logins
        $stmt1 = $conn->prepare("DELETE FROM doctor_logins WHERE doctor_id = ?");
        $stmt1->bind_param('i', $_POST['doctor_id']);
        $stmt1->execute();
        
        // Then delete from doctors
        $stmt2 = $conn->prepare("DELETE FROM doctors WHERE doctor_id = ?");
        $stmt2->bind_param('i', $_POST['doctor_id']);
        if ($stmt2->execute()) {
          echo "<div class='success'>Doctor deleted successfully!</div>";
        } else {
          echo "<div class='error'>Error deleting doctor: " . $stmt2->error . "</div>";
        }
        break;
    }
  }
}

// Get all doctors
$doctors = $conn->query("
  SELECT d.doctor_id, d.full_name, d.specialty, d.email, d.phone, dl.username
  FROM doctors d
  LEFT JOIN doctor_logins dl ON d.doctor_id = dl.doctor_id
  ORDER BY d.full_name
");

// Get doctor for editing if edit_id is set
$edit_doctor = null;
if (isset($_GET['edit_id'])) {
  $edit_stmt = $conn->prepare("SELECT * FROM doctors WHERE doctor_id = ?");
  $edit_stmt->bind_param('i', $_GET['edit_id']);
  $edit_stmt->execute();
  $edit_doctor = $edit_stmt->get_result()->fetch_assoc();
}
?>

<div class="dashboard">
  <h2>👨‍⚕️ Doctor Management</h2>
  <p style="color: #666; margin-bottom: 2rem;">
    Manage all doctors in the system
  </p>

  <!-- Add/Edit Doctor Form -->
  <div style="background: #f8f9fa; padding: 2rem; border-radius: 10px; margin-bottom: 2rem;">
    <h3><?= $edit_doctor ? 'Edit Doctor' : 'Add New Doctor' ?></h3>
    <form method="post" action="" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; align-items: end;">
      <input type="hidden" name="action" value="<?= $edit_doctor ? 'edit' : 'add' ?>">
      <?php if ($edit_doctor): ?>
        <input type="hidden" name="doctor_id" value="<?= $edit_doctor['doctor_id'] ?>">
      <?php endif; ?>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Full Name</label>
        <input type="text" name="full_name" required value="<?= $edit_doctor ? htmlspecialchars($edit_doctor['full_name']) : '' ?>">
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Specialty</label>
        <select name="specialty" required>
          <option value="">Select Specialty</option>
          <option value="GP – General Practitioner" <?= ($edit_doctor && $edit_doctor['specialty'] == 'GP – General Practitioner') ? 'selected' : '' ?>>GP – General Practitioner</option>
          <option value="ENT – Ear, Nose, Throat Specialist" <?= ($edit_doctor && $edit_doctor['specialty'] == 'ENT – Ear, Nose, Throat Specialist') ? 'selected' : '' ?>>ENT – Ear, Nose, Throat Specialist</option>
          <option value="PED – Pediatrician" <?= ($edit_doctor && $edit_doctor['specialty'] == 'PED – Pediatrician') ? 'selected' : '' ?>>PED – Pediatrician</option>
          <option value="GYN – Gynecologist" <?= ($edit_doctor && $edit_doctor['specialty'] == 'GYN – Gynecologist') ? 'selected' : '' ?>>GYN – Gynecologist</option>
          <option value="DENT – Dentist" <?= ($edit_doctor && $edit_doctor['specialty'] == 'DENT – Dentist') ? 'selected' : '' ?>>DENT – Dentist</option>
          <option value="ORT – Orthopedic Surgeon" <?= ($edit_doctor && $edit_doctor['specialty'] == 'ORT – Orthopedic Surgeon') ? 'selected' : '' ?>>ORT – Orthopedic Surgeon</option>
          <option value="CAR – Cardiologist" <?= ($edit_doctor && $edit_doctor['specialty'] == 'CAR – Cardiologist') ? 'selected' : '' ?>>CAR – Cardiologist</option>
          <option value="NEU – Neurologist" <?= ($edit_doctor && $edit_doctor['specialty'] == 'NEU – Neurologist') ? 'selected' : '' ?>>NEU – Neurologist</option>
          <option value="DER – Dermatologist" <?= ($edit_doctor && $edit_doctor['specialty'] == 'DER – Dermatologist') ? 'selected' : '' ?>>DER – Dermatologist</option>
          <option value="PSY – Psychiatrist" <?= ($edit_doctor && $edit_doctor['specialty'] == 'PSY – Psychiatrist') ? 'selected' : '' ?>>PSY – Psychiatrist</option>
          <option value="OPH – Ophthalmologist" <?= ($edit_doctor && $edit_doctor['specialty'] == 'OPH – Ophthalmologist') ? 'selected' : '' ?>>OPH – Ophthalmologist</option>
          <option value="URO – Urologist" <?= ($edit_doctor && $edit_doctor['specialty'] == 'URO – Urologist') ? 'selected' : '' ?>>URO – Urologist</option>
          <option value="END – Endocrinologist" <?= ($edit_doctor && $edit_doctor['specialty'] == 'END – Endocrinologist') ? 'selected' : '' ?>>END – Endocrinologist</option>
        </select>
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Email</label>
        <input type="email" name="email" value="<?= $edit_doctor ? htmlspecialchars($edit_doctor['email']) : '' ?>">
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Phone</label>
        <input type="tel" name="phone" value="<?= $edit_doctor ? htmlspecialchars($edit_doctor['phone']) : '' ?>">
      </div>
      
      <div style="display: flex; gap: 0.5rem;">
        <button type="submit" class="btn-primary"><?= $edit_doctor ? 'Update Doctor' : 'Add Doctor' ?></button>
        <?php if ($edit_doctor): ?>
          <a href="admin_doctors.php" style="background: #6c757d; color: white; padding: 0.75rem 1rem; border-radius: 5px; text-decoration: none;">Cancel</a>
        <?php endif; ?>
      </div>
    </form>
  </div>

  <!-- Doctors List -->
  <div>
    <h3>All Doctors</h3>
    <?php if ($doctors && $doctors->num_rows > 0): ?>
      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Specialty</th>
            <th>Email</th>
            <th>Phone</th>
            <th>Username</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <?php while($doctor = $doctors->fetch_assoc()): ?>
            <tr>
              <td><?= $doctor['doctor_id'] ?></td>
              <td><?= htmlspecialchars($doctor['full_name']) ?></td>
              <td><?= htmlspecialchars($doctor['specialty']) ?></td>
              <td><?= htmlspecialchars($doctor['email']) ?></td>
              <td><?= htmlspecialchars($doctor['phone']) ?></td>
              <td><?= htmlspecialchars($doctor['username'] ?? 'No login') ?></td>
              <td>
                <a href="?edit_id=<?= $doctor['doctor_id'] ?>" style="background: #007bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; text-decoration: none; margin-right: 0.5rem;">Edit</a>
                <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this doctor?')">
                  <input type="hidden" name="action" value="delete">
                  <input type="hidden" name="doctor_id" value="<?= $doctor['doctor_id'] ?>">
                  <button type="submit" style="background: #dc3545; color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 4px; cursor: pointer;">Delete</button>
                </form>
              </td>
            </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p style="text-align: center; color: #666; padding: 2rem;">No doctors found.</p>
    <?php endif; ?>
  </div>

  <div style="margin-top: 2rem; text-align: center;">
    <a href="admin_dashboard.php" class="btn-primary">← Back to Dashboard</a>
  </div>
</div>

<?php include 'includes/footer.php'; ?>

<?php
include 'includes/header.php';

// Check if user is logged in as admin
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
  header("Location: admin_login.php");
  exit;
}

// Handle doctor actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    switch ($_POST['action']) {
      case 'add':
        $stmt = $conn->prepare("INSERT INTO doctors (department_id, full_name, specialty, email, phone, username, password_hash) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $department_id = !empty($_POST['department_id']) ? $_POST['department_id'] : null;
        $password_hash = password_hash($_POST['password'], PASSWORD_BCRYPT);
        $stmt->bind_param('issssss', $department_id, $_POST['full_name'], $_POST['specialty'], $_POST['email'], $_POST['phone'], $_POST['username'], $password_hash);
        if ($stmt->execute()) {
          echo "<div class='success'>Doctor added successfully! Login: " . htmlspecialchars($_POST['username']) . " / " . htmlspecialchars($_POST['password']) . "</div>";
        } else {
          if ($stmt->errno == 1062) { // Duplicate entry error
            echo "<div class='error'>Username or email already exists. Please choose different credentials.</div>";
          } else {
            echo "<div class='error'>Error adding doctor: " . $stmt->error . "</div>";
          }
        }
        break;
        
      case 'edit':
        $stmt = $conn->prepare("UPDATE doctors SET department_id = ?, full_name = ?, specialty = ?, email = ?, phone = ? WHERE doctor_id = ?");
        $department_id = !empty($_POST['department_id']) ? $_POST['department_id'] : null;
        $stmt->bind_param('issssi', $department_id, $_POST['full_name'], $_POST['specialty'], $_POST['email'], $_POST['phone'], $_POST['doctor_id']);
        if ($stmt->execute()) {
          echo "<div class='success'>Doctor updated successfully!</div>";
        } else {
          if ($stmt->errno == 1062) { // Duplicate entry error
            echo "<div class='error'>Email already exists. Please choose a different email.</div>";
          } else {
            echo "<div class='error'>Error updating doctor: " . $stmt->error . "</div>";
          }
        }
        break;
        
      case 'delete':
        // Check if doctor has appointments
        $check_stmt = $conn->prepare("SELECT COUNT(*) as count FROM appointments WHERE doctor_id = ?");
        $check_stmt->bind_param('i', $_POST['doctor_id']);
        $check_stmt->execute();
        $result = $check_stmt->get_result();
        $appointment_count = $result->fetch_assoc()['count'];

        if ($appointment_count > 0) {
          echo "<div class='error'>Cannot delete doctor. They have $appointment_count appointment(s). Please reassign or cancel appointments first.</div>";
        } else {
          $stmt = $conn->prepare("DELETE FROM doctors WHERE doctor_id = ?");
          $stmt->bind_param('i', $_POST['doctor_id']);
          if ($stmt->execute()) {
            echo "<div class='success'>Doctor deleted successfully!</div>";
          } else {
            echo "<div class='error'>Error deleting doctor: " . $stmt->error . "</div>";
          }
        }
        break;
    }
  }
}

// Get all doctors with department info
$doctors = $conn->query("
  SELECT d.doctor_id, d.department_id, d.full_name, d.specialty, d.email, d.phone, d.username,
         dep.department_name, COUNT(a.appointment_id) as appointment_count
  FROM doctors d
  LEFT JOIN departments dep ON d.department_id = dep.department_id
  LEFT JOIN appointments a ON d.doctor_id = a.doctor_id
  GROUP BY d.doctor_id
  ORDER BY d.full_name
");

// Get departments for dropdown
$departments = $conn->query("SELECT department_id, department_name FROM departments ORDER BY department_name");

// Get doctor for editing if edit_id is set
$edit_doctor = null;
if (isset($_GET['edit_id'])) {
  $edit_stmt = $conn->prepare("SELECT * FROM doctors WHERE doctor_id = ?");
  $edit_stmt->bind_param('i', $_GET['edit_id']);
  $edit_stmt->execute();
  $edit_doctor = $edit_stmt->get_result()->fetch_assoc();
}
?>

<div class="dashboard">
  <h2>👨‍⚕️ Doctor Management</h2>
  <p style="color: #666; margin-bottom: 2rem;">
    Manage all doctors in the system
  </p>

  <!-- Add/Edit Doctor Form -->
  <div style="background: #f8f9fa; padding: 2rem; border-radius: 10px; margin-bottom: 2rem;">
    <h3><?= $edit_doctor ? 'Edit Doctor' : 'Add New Doctor' ?></h3>
    <form method="post" action="" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; align-items: end;">
      <input type="hidden" name="action" value="<?= $edit_doctor ? 'edit' : 'add' ?>">
      <?php if ($edit_doctor): ?>
        <input type="hidden" name="doctor_id" value="<?= $edit_doctor['doctor_id'] ?>">
      <?php endif; ?>

      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Department</label>
        <select name="department_id">
          <option value="">Select Department</option>
          <?php
          $departments->data_seek(0); // Reset pointer
          while($dept = $departments->fetch_assoc()): ?>
            <option value="<?= $dept['department_id'] ?>" <?= ($edit_doctor && $edit_doctor['department_id'] == $dept['department_id']) ? 'selected' : '' ?>>
              <?= htmlspecialchars($dept['department_name']) ?>
            </option>
          <?php endwhile; ?>
        </select>
      </div>

      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Full Name</label>
        <input type="text" name="full_name" required value="<?= $edit_doctor ? htmlspecialchars($edit_doctor['full_name']) : '' ?>">
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Specialty</label>
        <input type="text" name="specialty" value="<?= $edit_doctor ? htmlspecialchars($edit_doctor['specialty']) : '' ?>" placeholder="e.g., Cardiologist, General Practitioner">
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Email</label>
        <input type="email" name="email" value="<?= $edit_doctor ? htmlspecialchars($edit_doctor['email']) : '' ?>">
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Phone</label>
        <input type="tel" name="phone" value="<?= $edit_doctor ? htmlspecialchars($edit_doctor['phone']) : '' ?>">
      </div>

      <?php if (!$edit_doctor): ?>
        <div>
          <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Username</label>
          <input type="text" name="username" required placeholder="e.g., dr.john">
        </div>

        <div>
          <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Password</label>
          <input type="password" name="password" required placeholder="Strong password">
        </div>
      <?php endif; ?>

      <div style="display: flex; gap: 0.5rem;">
        <button type="submit" class="btn-primary"><?= $edit_doctor ? 'Update Doctor' : 'Add Doctor' ?></button>
        <?php if ($edit_doctor): ?>
          <a href="admin_doctors.php" style="background: #6c757d; color: white; padding: 0.75rem 1rem; border-radius: 5px; text-decoration: none;">Cancel</a>
        <?php endif; ?>
      </div>
    </form>
  </div>

  <!-- Doctors List -->
  <div>
    <h3>All Doctors</h3>
    <?php if ($doctors && $doctors->num_rows > 0): ?>
      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Department</th>
            <th>Specialty</th>
            <th>Contact</th>
            <th>Username</th>
            <th>Appointments</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <?php while($doctor = $doctors->fetch_assoc()): ?>
            <tr>
              <td><?= $doctor['doctor_id'] ?></td>
              <td><?= htmlspecialchars($doctor['full_name']) ?></td>
              <td>
                <?php if ($doctor['department_name']): ?>
                  <span style="background: #007bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                    <?= htmlspecialchars($doctor['department_name']) ?>
                  </span>
                <?php else: ?>
                  <span style="color: #666;">No Department</span>
                <?php endif; ?>
              </td>
              <td><?= htmlspecialchars($doctor['specialty']) ?></td>
              <td>
                <small>
                  📧 <?= htmlspecialchars($doctor['email']) ?><br>
                  📞 <?= htmlspecialchars($doctor['phone']) ?>
                </small>
              </td>
              <td><?= htmlspecialchars($doctor['username']) ?></td>
              <td>
                <span style="background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                  <?= $doctor['appointment_count'] ?> appointments
                </span>
              </td>
              <td>
                <a href="?edit_id=<?= $doctor['doctor_id'] ?>" style="background: #007bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; text-decoration: none; margin-right: 0.5rem;">Edit</a>
                <?php if ($doctor['appointment_count'] == 0): ?>
                  <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this doctor?')">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="doctor_id" value="<?= $doctor['doctor_id'] ?>">
                    <button type="submit" style="background: #dc3545; color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 4px; cursor: pointer;">Delete</button>
                  </form>
                <?php else: ?>
                  <span style="background: #6c757d; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">Has Appointments</span>
                <?php endif; ?>
              </td>
            </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p style="text-align: center; color: #666; padding: 2rem;">No doctors found.</p>
    <?php endif; ?>
  </div>

  <div style="margin-top: 2rem; text-align: center;">
    <a href="admin_dashboard.php" class="btn-primary">← Back to Dashboard</a>
  </div>
</div>

<?php include 'includes/footer.php'; ?>

<?php
include 'includes/header.php';

// Check if user is logged in as doctor
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'doctor') {
  header("Location: doctor_login.php");
  exit;
}

// Get doctor info
$doctor_id = $_SESSION['user_id'];
$doctor_name = $_SESSION['doctor_name'];
$doctor_specialty = $_SESSION['doctor_specialty'];

// Get doctor's statistics
$stats = [];

// Total appointments for this doctor
$total_appointments = $conn->prepare("SELECT COUNT(*) as count FROM appointments WHERE doctor_id = ?");
$total_appointments->bind_param('i', $doctor_id);
$total_appointments->execute();
$total_apt_count = $total_appointments->get_result()->fetch_assoc()['count'];

// Today's appointments for this doctor
$today_appointments = $conn->prepare("SELECT COUNT(*) as count FROM appointments WHERE doctor_id = ? AND DATE(appointment_date) = CURDATE()");
$today_appointments->bind_param('i', $doctor_id);
$today_appointments->execute();
$today_apt_count = $today_appointments->get_result()->fetch_assoc()['count'];

// Upcoming appointments for this doctor
$upcoming_appointments = $conn->prepare("SELECT COUNT(*) as count FROM appointments WHERE doctor_id = ? AND appointment_date > NOW()");
$upcoming_appointments->bind_param('i', $doctor_id);
$upcoming_appointments->execute();
$upcoming_apt_count = $upcoming_appointments->get_result()->fetch_assoc()['count'];

// Recent appointments for this doctor (last 5)
$recent_appointments = $conn->prepare("
  SELECT a.appointment_id, p.full_name as patient_name, p.email, p.phone,
         a.appointment_date, a.consultation_type
  FROM appointments a
  JOIN patients p ON a.patient_id = p.patient_id
  WHERE a.doctor_id = ?
  ORDER BY a.appointment_date DESC
  LIMIT 5
");
$recent_appointments->bind_param('i', $doctor_id);
$recent_appointments->execute();
$recent_apt_result = $recent_appointments->get_result();

// Today's appointments for this doctor
$todays_appointments = $conn->prepare("
  SELECT a.appointment_id, p.full_name as patient_name, p.email, p.phone,
         a.appointment_date, a.consultation_type
  FROM appointments a
  JOIN patients p ON a.patient_id = p.patient_id
  WHERE a.doctor_id = ? AND DATE(a.appointment_date) = CURDATE()
  ORDER BY a.appointment_date ASC
");
$todays_appointments->bind_param('i', $doctor_id);
$todays_appointments->execute();
$todays_apt_result = $todays_appointments->get_result();
?>

<div class="dashboard">
  <h2>Welcome, Dr. <?= htmlspecialchars($doctor_name) ?>! 👨‍⚕️</h2>
  <p style="color: #666; margin-bottom: 2rem;">
    Specialty: <?= htmlspecialchars($doctor_specialty) ?>
  </p>

  <!-- Doctor Statistics -->
  <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 10px; margin: 2rem 0;">
    <h3>Your Practice Overview</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">📅</div>
        <strong><?= $total_apt_count ?></strong><br>
        <small>Total Appointments</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">📋</div>
        <strong><?= $today_apt_count ?></strong><br>
        <small>Today's Appointments</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">⏰</div>
        <strong><?= $upcoming_apt_count ?></strong><br>
        <small>Upcoming Appointments</small>
      </div>
    </div>
  </div>

  <!-- Doctor Actions -->
  <div class="dashboard-grid">
    <a href="doctor_appointments.php" class="dashboard-card">
      <h3>📅 My Appointments</h3>
      <p>View all your scheduled appointments</p>
    </a>

    <a href="doctor_patients.php" class="dashboard-card">
      <h3>👥 My Patients</h3>
      <p>View patients you've treated</p>
    </a>

    <a href="doctor_schedule.php" class="dashboard-card">
      <h3>🗓️ Schedule</h3>
      <p>Manage your availability and schedule</p>
    </a>
  </div>

  <!-- Today's Appointments -->
  <div style="margin-top: 3rem;">
    <h3>Today's Appointments</h3>
    <?php if ($todays_apt_result && $todays_apt_result->num_rows > 0): ?>
      <table>
        <thead>
          <tr>
            <th>Time</th>
            <th>Patient</th>
            <th>Contact</th>
            <th>Type</th>
          </tr>
        </thead>
        <tbody>
          <?php while($apt = $todays_apt_result->fetch_assoc()): ?>
            <tr>
              <td><?= date('g:i A', strtotime($apt['appointment_date'])) ?></td>
              <td><?= htmlspecialchars($apt['patient_name']) ?></td>
              <td>
                <?= htmlspecialchars($apt['email']) ?><br>
                <small><?= htmlspecialchars($apt['phone']) ?></small>
              </td>
              <td><?= htmlspecialchars($apt['consultation_type']) ?></td>
            </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p style="text-align: center; color: #666; padding: 2rem;">No appointments scheduled for today.</p>
    <?php endif; ?>
  </div>

  <!-- Recent Appointments -->
  <div style="margin-top: 3rem;">
    <h3>Recent Appointments</h3>
    <?php if ($recent_apt_result && $recent_apt_result->num_rows > 0): ?>
      <table>
        <thead>
          <tr>
            <th>Date & Time</th>
            <th>Patient</th>
            <th>Contact</th>
            <th>Type</th>
          </tr>
        </thead>
        <tbody>
          <?php while($apt = $recent_apt_result->fetch_assoc()): ?>
            <tr>
              <td><?= date('M j, Y g:i A', strtotime($apt['appointment_date'])) ?></td>
              <td><?= htmlspecialchars($apt['patient_name']) ?></td>
              <td>
                <?= htmlspecialchars($apt['email']) ?><br>
                <small><?= htmlspecialchars($apt['phone']) ?></small>
              </td>
              <td><?= htmlspecialchars($apt['consultation_type']) ?></td>
            </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p style="text-align: center; color: #666; padding: 2rem;">No recent appointments found.</p>
    <?php endif; ?>
  </div>
</div>

<?php include 'includes/footer.php'; ?>

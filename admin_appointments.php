<?php
include 'includes/header.php';

// Check if user is logged in as admin
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
  header("Location: admin_login.php");
  exit;
}

// Handle appointment actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    switch ($_POST['action']) {
      case 'add':
        $stmt = $conn->prepare("INSERT INTO appointments (patient_id, doctor_id, appointment_date, consultation_type, status, created_by) VALUES (?, ?, ?, ?, ?, 'admin')");
        $appointment_datetime = $_POST['date'] . ' ' . $_POST['time'];
        $status = $_POST['status'] ?? 'Scheduled';
        $stmt->bind_param('iisss', $_POST['patient_id'], $_POST['doctor_id'], $appointment_datetime, $_POST['consultation_type'], $status);
        if ($stmt->execute()) {
          echo "<div class='success'>Appointment added successfully!</div>";
        } else {
          echo "<div class='error'>Error adding appointment: " . $stmt->error . "</div>";
        }
        break;

      case 'update_status':
        $stmt = $conn->prepare("UPDATE appointments SET status = ?, diagnosis = ? WHERE appointment_id = ?");
        $stmt->bind_param('ssi', $_POST['status'], $_POST['diagnosis'], $_POST['appointment_id']);
        if ($stmt->execute()) {
          echo "<div class='success'>Appointment updated successfully!</div>";
        } else {
          echo "<div class='error'>Error updating appointment: " . $stmt->error . "</div>";
        }
        break;
        
      case 'delete':
        $stmt = $conn->prepare("DELETE FROM appointments WHERE appointment_id = ?");
        $stmt->bind_param('i', $_POST['appointment_id']);
        if ($stmt->execute()) {
          echo "<div class='success'>Appointment deleted successfully!</div>";
        } else {
          echo "<div class='error'>Error deleting appointment: " . $stmt->error . "</div>";
        }
        break;
    }
  }
}

// Get all appointments with patient and doctor info
$appointments = $conn->query("
  SELECT a.appointment_id, p.full_name as patient_name, d.full_name as doctor_name,
         a.appointment_date, a.consultation_type, a.status, a.diagnosis, a.created_by,
         p.patient_id, d.doctor_id, dep.department_name
  FROM appointments a
  JOIN patients p ON a.patient_id = p.patient_id
  JOIN doctors d ON a.doctor_id = d.doctor_id
  LEFT JOIN departments dep ON d.department_id = dep.department_id
  ORDER BY a.appointment_date DESC
");

// Get patients for dropdown
$patients = $conn->query("SELECT patient_id, full_name FROM patients ORDER BY full_name");

// Get doctors for dropdown
$doctors = $conn->query("
  SELECT d.doctor_id, d.full_name, d.specialty, dep.department_name
  FROM doctors d
  LEFT JOIN departments dep ON d.department_id = dep.department_id
  ORDER BY d.full_name
");
?>

<div class="dashboard">
  <h2>📅 Appointment Management</h2>
  <p style="color: #666; margin-bottom: 2rem;">
    Manage all appointments in the system
  </p>

  <!-- Add New Appointment Form -->
  <div style="background: #f8f9fa; padding: 2rem; border-radius: 10px; margin-bottom: 2rem;">
    <h3>Add New Appointment</h3>
    <form method="post" action="" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
      <input type="hidden" name="action" value="add">
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Patient</label>
        <select name="patient_id" required>
          <option value="">Select Patient</option>
          <?php 
          $patients->data_seek(0); // Reset pointer
          while($patient = $patients->fetch_assoc()): ?>
            <option value="<?= $patient['patient_id'] ?>"><?= htmlspecialchars($patient['full_name']) ?></option>
          <?php endwhile; ?>
        </select>
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Doctor</label>
        <select name="doctor_id" required>
          <option value="">Select Doctor</option>
          <?php
          $doctors->data_seek(0); // Reset pointer
          while($doctor = $doctors->fetch_assoc()): ?>
            <option value="<?= $doctor['doctor_id'] ?>">
              <?= htmlspecialchars($doctor['full_name']) ?> - <?= htmlspecialchars($doctor['specialty']) ?>
              <?php if ($doctor['department_name']): ?>
                (<?= htmlspecialchars($doctor['department_name']) ?>)
              <?php endif; ?>
            </option>
          <?php endwhile; ?>
        </select>
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Date</label>
        <input type="date" name="date" required min="<?= date('Y-m-d') ?>">
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Time</label>
        <input type="time" name="time" required>
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Type</label>
        <select name="consultation_type" required>
          <option value="">Select Type</option>
          <option value="General Consultation">General Consultation</option>
          <option value="Follow-up">Follow-up</option>
          <option value="Emergency">Emergency</option>
          <option value="Specialist Consultation">Specialist Consultation</option>
        </select>
      </div>

      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Status</label>
        <select name="status" required>
          <option value="Scheduled">Scheduled</option>
          <option value="Completed">Completed</option>
          <option value="Cancelled">Cancelled</option>
          <option value="No-Show">No-Show</option>
        </select>
      </div>

      <button type="submit" class="btn-primary">Add Appointment</button>
    </form>
  </div>

  <!-- Appointments List -->
  <div>
    <h3>All Appointments</h3>
    <?php if ($appointments && $appointments->num_rows > 0): ?>
      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>Patient</th>
            <th>Doctor/Department</th>
            <th>Date & Time</th>
            <th>Type</th>
            <th>Status</th>
            <th>Diagnosis</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <?php while($apt = $appointments->fetch_assoc()): ?>
            <tr>
              <td><?= $apt['appointment_id'] ?></td>
              <td><?= htmlspecialchars($apt['patient_name']) ?></td>
              <td>
                <?= htmlspecialchars($apt['doctor_name']) ?><br>
                <?php if ($apt['department_name']): ?>
                  <small style="color: #666;"><?= htmlspecialchars($apt['department_name']) ?></small>
                <?php endif; ?>
              </td>
              <td><?= date('M j, Y g:i A', strtotime($apt['appointment_date'])) ?></td>
              <td><?= htmlspecialchars($apt['consultation_type']) ?></td>
              <td>
                <?php
                $status_colors = [
                  'Scheduled' => 'background: #007bff; color: white;',
                  'Completed' => 'background: #28a745; color: white;',
                  'Cancelled' => 'background: #dc3545; color: white;',
                  'No-Show' => 'background: #6c757d; color: white;'
                ];
                $status_color = $status_colors[$apt['status']] ?? 'background: #6c757d; color: white;';
                ?>
                <span style="padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem; <?= $status_color ?>">
                  <?= htmlspecialchars($apt['status']) ?>
                </span>
              </td>
              <td>
                <small><?= htmlspecialchars($apt['diagnosis'] ?? 'Not specified') ?></small>
              </td>
              <td>
                <button onclick="updateAppointment(<?= $apt['appointment_id'] ?>, '<?= htmlspecialchars($apt['status']) ?>', '<?= htmlspecialchars($apt['diagnosis']) ?>')"
                        style="background: #28a745; color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 4px; cursor: pointer; margin-right: 0.5rem;">
                  Update
                </button>
                <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this appointment?')">
                  <input type="hidden" name="action" value="delete">
                  <input type="hidden" name="appointment_id" value="<?= $apt['appointment_id'] ?>">
                  <button type="submit" style="background: #dc3545; color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 4px; cursor: pointer;">Delete</button>
                </form>
              </td>
            </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p style="text-align: center; color: #666; padding: 2rem;">No appointments found.</p>
    <?php endif; ?>
  </div>

  <div style="margin-top: 2rem; text-align: center;">
    <a href="admin_dashboard.php" class="btn-primary">← Back to Dashboard</a>
  </div>
</div>

<!-- Update Appointment Modal -->
<div id="updateModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
  <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 10px; width: 90%; max-width: 500px;">
    <h3>Update Appointment</h3>
    <form method="post" action="">
      <input type="hidden" name="action" value="update_status">
      <input type="hidden" name="appointment_id" id="modal_appointment_id">

      <div style="margin-bottom: 1rem;">
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Status</label>
        <select name="status" id="modal_status" required>
          <option value="Scheduled">Scheduled</option>
          <option value="Completed">Completed</option>
          <option value="Cancelled">Cancelled</option>
          <option value="No-Show">No-Show</option>
        </select>
      </div>

      <div style="margin-bottom: 1rem;">
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Diagnosis</label>
        <textarea name="diagnosis" id="modal_diagnosis" rows="3" placeholder="Enter diagnosis or notes"></textarea>
      </div>

      <div style="display: flex; gap: 1rem;">
        <button type="submit" class="btn-primary">Update Appointment</button>
        <button type="button" onclick="closeModal()" style="background: #6c757d; color: white; padding: 0.75rem 1rem; border: none; border-radius: 5px; cursor: pointer;">Cancel</button>
      </div>
    </form>
  </div>
</div>

<script>
function updateAppointment(appointmentId, currentStatus, currentDiagnosis) {
  document.getElementById('modal_appointment_id').value = appointmentId;
  document.getElementById('modal_status').value = currentStatus;
  document.getElementById('modal_diagnosis').value = currentDiagnosis || '';
  document.getElementById('updateModal').style.display = 'block';
}

function closeModal() {
  document.getElementById('updateModal').style.display = 'none';
}

// Close modal when clicking outside
document.getElementById('updateModal').addEventListener('click', function(e) {
  if (e.target === this) {
    closeModal();
  }
});
</script>

<?php include 'includes/footer.php'; ?>

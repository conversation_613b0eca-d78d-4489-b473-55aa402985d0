<?php
include 'includes/header.php';

// Check if user is logged in as admin
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
  header("Location: admin_login.php");
  exit;
}

// Handle appointment actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    switch ($_POST['action']) {
      case 'add':
        $stmt = $conn->prepare("INSERT INTO appointments (patient_id, doctor_id, appointment_date, consultation_type) VALUES (?, ?, ?, ?)");
        $appointment_datetime = $_POST['date'] . ' ' . $_POST['time'];
        $stmt->bind_param('iiss', $_POST['patient_id'], $_POST['doctor_id'], $appointment_datetime, $_POST['consultation_type']);
        if ($stmt->execute()) {
          echo "<div class='success'>Appointment added successfully!</div>";
        } else {
          echo "<div class='error'>Error adding appointment: " . $stmt->error . "</div>";
        }
        break;
        
      case 'delete':
        $stmt = $conn->prepare("DELETE FROM appointments WHERE appointment_id = ?");
        $stmt->bind_param('i', $_POST['appointment_id']);
        if ($stmt->execute()) {
          echo "<div class='success'>Appointment deleted successfully!</div>";
        } else {
          echo "<div class='error'>Error deleting appointment: " . $stmt->error . "</div>";
        }
        break;
    }
  }
}

// Get all appointments with patient and doctor info
$appointments = $conn->query("
  SELECT a.appointment_id, p.full_name as patient_name, d.full_name as doctor_name,
         a.appointment_date, a.consultation_type, p.patient_id, d.doctor_id
  FROM appointments a
  JOIN patients p ON a.patient_id = p.patient_id
  JOIN doctors d ON a.doctor_id = d.doctor_id
  ORDER BY a.appointment_date DESC
");

// Get patients for dropdown
$patients = $conn->query("SELECT patient_id, full_name FROM patients ORDER BY full_name");

// Get doctors for dropdown
$doctors = $conn->query("SELECT doctor_id, full_name, specialty FROM doctors ORDER BY full_name");
?>

<div class="dashboard">
  <h2>📅 Appointment Management</h2>
  <p style="color: #666; margin-bottom: 2rem;">
    Manage all appointments in the system
  </p>

  <!-- Add New Appointment Form -->
  <div style="background: #f8f9fa; padding: 2rem; border-radius: 10px; margin-bottom: 2rem;">
    <h3>Add New Appointment</h3>
    <form method="post" action="" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
      <input type="hidden" name="action" value="add">
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Patient</label>
        <select name="patient_id" required>
          <option value="">Select Patient</option>
          <?php 
          $patients->data_seek(0); // Reset pointer
          while($patient = $patients->fetch_assoc()): ?>
            <option value="<?= $patient['patient_id'] ?>"><?= htmlspecialchars($patient['full_name']) ?></option>
          <?php endwhile; ?>
        </select>
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Doctor</label>
        <select name="doctor_id" required>
          <option value="">Select Doctor</option>
          <?php 
          $doctors->data_seek(0); // Reset pointer
          while($doctor = $doctors->fetch_assoc()): ?>
            <option value="<?= $doctor['doctor_id'] ?>"><?= htmlspecialchars($doctor['full_name']) ?> - <?= htmlspecialchars($doctor['specialty']) ?></option>
          <?php endwhile; ?>
        </select>
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Date</label>
        <input type="date" name="date" required min="<?= date('Y-m-d') ?>">
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Time</label>
        <input type="time" name="time" required>
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Type</label>
        <select name="consultation_type" required>
          <option value="">Select Type</option>
          <option value="General Consultation">General Consultation</option>
          <option value="Follow-up">Follow-up</option>
          <option value="Emergency">Emergency</option>
          <option value="Specialist Consultation">Specialist Consultation</option>
        </select>
      </div>
      
      <button type="submit" class="btn-primary">Add Appointment</button>
    </form>
  </div>

  <!-- Appointments List -->
  <div>
    <h3>All Appointments</h3>
    <?php if ($appointments && $appointments->num_rows > 0): ?>
      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>Patient</th>
            <th>Doctor</th>
            <th>Date & Time</th>
            <th>Type</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <?php while($apt = $appointments->fetch_assoc()): ?>
            <tr>
              <td><?= $apt['appointment_id'] ?></td>
              <td><?= htmlspecialchars($apt['patient_name']) ?></td>
              <td><?= htmlspecialchars($apt['doctor_name']) ?></td>
              <td><?= date('M j, Y g:i A', strtotime($apt['appointment_date'])) ?></td>
              <td><?= htmlspecialchars($apt['consultation_type']) ?></td>
              <td>
                <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this appointment?')">
                  <input type="hidden" name="action" value="delete">
                  <input type="hidden" name="appointment_id" value="<?= $apt['appointment_id'] ?>">
                  <button type="submit" style="background: #dc3545; color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 4px; cursor: pointer;">Delete</button>
                </form>
              </td>
            </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p style="text-align: center; color: #666; padding: 2rem;">No appointments found.</p>
    <?php endif; ?>
  </div>

  <div style="margin-top: 2rem; text-align: center;">
    <a href="admin_dashboard.php" class="btn-primary">← Back to Dashboard</a>
  </div>
</div>

<?php include 'includes/footer.php'; ?>

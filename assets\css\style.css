/* MediCarePlus - Modern Healthcare UI */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

header h1 a {
    color: #2c5aa0;
    text-decoration: none;
    font-size: 1.8rem;
    font-weight: bold;
}

nav {
    display: flex;
    gap: 1rem;
}

nav a {
    color: #2c5aa0;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 500;
}

nav a:hover {
    background: #2c5aa0;
    color: white;
    transform: translateY(-2px);
}

/* Main Content */
main {
    background: white;
    margin: 2rem auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 1000px;
}

.content-wrapper {
    padding: 2rem;
}

/* Home Page Hero */
.home-hero {
    text-align: center;
    padding: 3rem 2rem;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.home-hero h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 300;
}

.home-hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn:hover {
    background: white;
    color: #f5576c;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: #2c5aa0;
    border-color: #2c5aa0;
    color: white;
}

.btn-primary:hover {
    background: #1e3f73;
    transform: translateY(-2px);
}

/* Features Section */
.features {
    padding: 2rem;
    background: #f8f9fa;
}

.features h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2c5aa0;
    font-size: 2rem;
}

.features ul {
    list-style: none;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.features li {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    font-size: 1.1rem;
}

.features li:hover {
    transform: translateY(-5px);
}

/* Forms */
.form-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 2rem;
}

.form-container h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2c5aa0;
    font-size: 2rem;
}

form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

input, select, textarea {
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #2c5aa0;
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

button {
    padding: 12px 20px;
    background: #2c5aa0;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

button:hover {
    background: #1e3f73;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Dashboard */
.dashboard {
    padding: 2rem;
}

.dashboard h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2c5aa0;
    font-size: 2.5rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    color: white;
}

.dashboard-card h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.dashboard-card p {
    opacity: 0.9;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 2rem;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

th, td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #e1e5e9;
}

th {
    background: #2c5aa0;
    color: white;
    font-weight: 600;
}

tr:hover {
    background: #f8f9fa;
}

/* Messages */
.success {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #c3e6cb;
    margin: 1rem 0;
}

.error {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #f5c6cb;
    margin: 1rem 0;
}

/* Footer */
footer {
    background: #2c5aa0;
    color: white;
    text-align: center;
    padding: 2rem;
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .home-hero h2 {
        font-size: 2rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    table {
        font-size: 0.9rem;
    }
    
    th, td {
        padding: 10px;
    }
}

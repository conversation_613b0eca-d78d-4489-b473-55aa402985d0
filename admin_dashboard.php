<?php
include 'includes/header.php';

// Check if user is logged in as admin
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
  header("Location: admin_login.php");
  exit;
}

// Get admin info
$admin_name = $_SESSION['admin_name'];
$admin_role = $_SESSION['admin_role'];

// Get system statistics
$stats = [];

// Total patients
$patient_count = $conn->query("SELECT COUNT(*) as count FROM patients")->fetch_assoc()['count'];

// Total doctors
$doctor_count = $conn->query("SELECT COUNT(*) as count FROM doctors")->fetch_assoc()['count'];

// Total appointments
$appointment_count = $conn->query("SELECT COUNT(*) as count FROM appointments")->fetch_assoc()['count'];

// Today's appointments
$today_appointments = $conn->query("SELECT COUNT(*) as count FROM appointments WHERE DATE(appointment_date) = CURDATE()")->fetch_assoc()['count'];

// Recent appointments (last 5)
$recent_appointments = $conn->query("
  SELECT a.appointment_id, p.full_name as patient_name, d.full_name as doctor_name, 
         a.appointment_date, a.consultation_type
  FROM appointments a
  JOIN patients p ON a.patient_id = p.patient_id
  JOIN doctors d ON a.doctor_id = d.doctor_id
  ORDER BY a.appointment_date DESC
  LIMIT 5
");
?>

<div class="dashboard">
  <h2>Welcome, <?= htmlspecialchars($admin_name) ?>! 👨‍💼</h2>
  <p style="color: #666; margin-bottom: 2rem;">
    Admin Dashboard - <?= ucfirst($admin_role) ?> Access
  </p>

  <!-- System Statistics -->
  <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 10px; margin: 2rem 0;">
    <h3>System Overview</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">👥</div>
        <strong><?= $patient_count ?></strong><br>
        <small>Total Patients</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">👨‍⚕️</div>
        <strong><?= $doctor_count ?></strong><br>
        <small>Total Doctors</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">📅</div>
        <strong><?= $appointment_count ?></strong><br>
        <small>Total Appointments</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">📋</div>
        <strong><?= $today_appointments ?></strong><br>
        <small>Today's Appointments</small>
      </div>
    </div>
  </div>

  <!-- Admin Actions -->
  <div class="dashboard-grid">
    <a href="admin_appointments.php" class="dashboard-card">
      <h3>📅 Manage Appointments</h3>
      <p>View, add, edit, and manage all appointments</p>
    </a>

    <a href="admin_doctors.php" class="dashboard-card">
      <h3>👨‍⚕️ Manage Doctors</h3>
      <p>Add, edit, and manage doctor profiles</p>
    </a>

    <a href="admin_patients.php" class="dashboard-card">
      <h3>👥 Manage Patients</h3>
      <p>View and manage patient information</p>
    </a>

    <a href="admin_reports.php" class="dashboard-card">
      <h3>📊 Reports</h3>
      <p>View system reports and analytics</p>
    </a>
  </div>

  <!-- Recent Appointments -->
  <div style="margin-top: 3rem;">
    <h3>Recent Appointments</h3>
    <?php if ($recent_appointments && $recent_appointments->num_rows > 0): ?>
      <table>
        <thead>
          <tr>
            <th>Patient</th>
            <th>Doctor</th>
            <th>Date & Time</th>
            <th>Type</th>
          </tr>
        </thead>
        <tbody>
          <?php while($apt = $recent_appointments->fetch_assoc()): ?>
            <tr>
              <td><?= htmlspecialchars($apt['patient_name']) ?></td>
              <td><?= htmlspecialchars($apt['doctor_name']) ?></td>
              <td><?= date('M j, Y g:i A', strtotime($apt['appointment_date'])) ?></td>
              <td><?= htmlspecialchars($apt['consultation_type']) ?></td>
            </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p style="text-align: center; color: #666; padding: 2rem;">No appointments found.</p>
    <?php endif; ?>
  </div>
</div>

<?php include 'includes/footer.php'; ?>

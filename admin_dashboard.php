<?php
include 'includes/header.php';

// Check if user is logged in as admin
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
  header("Location: admin_login.php");
  exit;
}

// Get admin info
$admin_name = $_SESSION['admin_name'];
$admin_role = $_SESSION['admin_role'];

// Get system statistics
$stats = [];

// Total patients
$patient_count = $conn->query("SELECT COUNT(*) as count FROM patients")->fetch_assoc()['count'];

// Total doctors
$doctor_count = $conn->query("SELECT COUNT(*) as count FROM doctors")->fetch_assoc()['count'];

// Total departments
$department_count = $conn->query("SELECT COUNT(*) as count FROM departments")->fetch_assoc()['count'];

// Total appointments
$appointment_count = $conn->query("SELECT COUNT(*) as count FROM appointments")->fetch_assoc()['count'];

// Today's appointments
$today_appointments = $conn->query("SELECT COUNT(*) as count FROM appointments WHERE DATE(appointment_date) = CURDATE()")->fetch_assoc()['count'];

// Pending invoices
$pending_invoices = $conn->query("SELECT COUNT(*) as count FROM invoices WHERE payment_status = 'Pending'")->fetch_assoc()['count'];

// Total revenue
$total_revenue = $conn->query("SELECT COALESCE(SUM(amount), 0) as total FROM invoices WHERE payment_status = 'Paid'")->fetch_assoc()['total'];

// Recent appointments (last 5)
$recent_appointments = $conn->query("
  SELECT a.appointment_id, p.full_name as patient_name, d.full_name as doctor_name,
         a.appointment_date, a.consultation_type, a.status, dep.department_name
  FROM appointments a
  JOIN patients p ON a.patient_id = p.patient_id
  JOIN doctors d ON a.doctor_id = d.doctor_id
  LEFT JOIN departments dep ON d.department_id = dep.department_id
  ORDER BY a.appointment_date DESC
  LIMIT 5
");
?>

<div class="dashboard">
  <h2>Welcome, <?= htmlspecialchars($admin_name) ?>! 👨‍💼</h2>
  <p style="color: #666; margin-bottom: 2rem;">
    Admin Dashboard - <?= ucfirst($admin_role) ?> Access
  </p>

  <!-- System Statistics -->
  <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 10px; margin: 2rem 0;">
    <h3>System Overview</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 1rem; margin-top: 1rem;">
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">👥</div>
        <strong><?= $patient_count ?></strong><br>
        <small>Total Patients</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">👨‍⚕️</div>
        <strong><?= $doctor_count ?></strong><br>
        <small>Total Doctors</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">🏥</div>
        <strong><?= $department_count ?></strong><br>
        <small>Departments</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">📅</div>
        <strong><?= $appointment_count ?></strong><br>
        <small>Total Appointments</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">📋</div>
        <strong><?= $today_appointments ?></strong><br>
        <small>Today's Appointments</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">💰</div>
        <strong>LKR <?= number_format($total_revenue, 0) ?></strong><br>
        <small>Total Revenue</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">⏳</div>
        <strong><?= $pending_invoices ?></strong><br>
        <small>Pending Invoices</small>
      </div>
    </div>
  </div>

  <!-- Admin Actions -->
  <div class="dashboard-grid">
    <a href="admin_appointments.php" class="dashboard-card">
      <h3>📅 Manage Appointments</h3>
      <p>View, add, edit, and manage all appointments</p>
    </a>

    <a href="admin_doctors.php" class="dashboard-card">
      <h3>👨‍⚕️ Manage Doctors</h3>
      <p>Add, edit, and manage doctor profiles</p>
    </a>

    <a href="admin_patients.php" class="dashboard-card">
      <h3>👥 Manage Patients</h3>
      <p>View and manage patient information</p>
    </a>

    <a href="admin_departments.php" class="dashboard-card">
      <h3>🏥 Manage Departments</h3>
      <p>Organize doctors by medical departments</p>
    </a>

    <a href="admin_invoices.php" class="dashboard-card">
      <h3>💰 Manage Invoices</h3>
      <p>Create and track patient invoices</p>
    </a>

    <a href="admin_reports.php" class="dashboard-card">
      <h3>📊 Reports</h3>
      <p>View system reports and analytics</p>
    </a>
  </div>

  <!-- Recent Appointments -->
  <div style="margin-top: 3rem;">
    <h3>Recent Appointments</h3>
    <?php if ($recent_appointments && $recent_appointments->num_rows > 0): ?>
      <table>
        <thead>
          <tr>
            <th>Patient</th>
            <th>Doctor/Department</th>
            <th>Date & Time</th>
            <th>Type</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
          <?php while($apt = $recent_appointments->fetch_assoc()): ?>
            <tr>
              <td><?= htmlspecialchars($apt['patient_name']) ?></td>
              <td>
                <?= htmlspecialchars($apt['doctor_name']) ?><br>
                <?php if ($apt['department_name']): ?>
                  <small style="color: #666;"><?= htmlspecialchars($apt['department_name']) ?></small>
                <?php endif; ?>
              </td>
              <td><?= date('M j, Y g:i A', strtotime($apt['appointment_date'])) ?></td>
              <td><?= htmlspecialchars($apt['consultation_type']) ?></td>
              <td>
                <?php
                $status_colors = [
                  'Scheduled' => 'background: #007bff; color: white;',
                  'Completed' => 'background: #28a745; color: white;',
                  'Cancelled' => 'background: #dc3545; color: white;',
                  'No-Show' => 'background: #6c757d; color: white;'
                ];
                $status_color = $status_colors[$apt['status']] ?? 'background: #6c757d; color: white;';
                ?>
                <span style="padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem; <?= $status_color ?>">
                  <?= htmlspecialchars($apt['status']) ?>
                </span>
              </td>
            </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p style="text-align: center; color: #666; padding: 2rem;">No appointments found.</p>
    <?php endif; ?>
  </div>
</div>

<?php include 'includes/footer.php'; ?>

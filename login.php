<?php include 'includes/header.php'; ?>

<div class="form-container">
  <h2>🔐 Patient Login</h2>
  <p style="text-align: center; margin-bottom: 2rem; color: #666;">
    Welcome back! Please sign in to your account
  </p>

  <?php if (isset($_GET['reg']) && $_GET['reg'] == '1'): ?>
    <div class="success">
      🎉 Registration successful! You can now login with your credentials.
    </div>
  <?php endif; ?>

  <?php if (isset($_GET['logout']) && $_GET['logout'] == '1'): ?>
    <div class="success">
      🎉 You have been successfully logged out.
    </div>
  <?php endif; ?>

  <form method="post" action="">
    <div style="position: relative;">
      <input name="username" placeholder="Username" required style="padding-left: 40px;">
      <span style="position: absolute; left: 12px; top: 12px; color: #666;">👤</span>
    </div>

    <div style="position: relative;">
      <input name="password" type="password" placeholder="Password" required style="padding-left: 40px;">
      <span style="position: absolute; left: 12px; top: 12px; color: #666;">🔒</span>
    </div>

    <button type="submit" class="btn-primary">🚪 Sign In</button>
  </form>

  <p style="text-align: center; margin-top: 2rem;">
    Don't have an account? <a href="register.php" style="color: #2c5aa0; text-decoration: none;">Register here</a>
  </p>

  <p style="text-align: center; margin-top: 1rem;">
    <a href="index.php" style="color: #2c5aa0; text-decoration: none;">← Back to Home</a> |
    <a href="doctor_login.php" style="color: #2c5aa0; text-decoration: none;">Doctor Login</a> |
    <a href="admin_login.php" style="color: #2c5aa0; text-decoration: none;">Admin Login</a>
  </p>
</div>
<?php
if ($_SERVER['REQUEST_METHOD']==='POST') {
  // Patient login
  $s = $conn->prepare(
    "SELECT pl.patient_id, pl.password_hash
     FROM patient_logins pl
     WHERE pl.username=?"
  );

  if ($s === false) {
    echo "<p class='error'>Database error: " . $conn->error . "</p>";
    echo "<p class='error'>Please make sure the patient_logins table exists with the correct structure.</p>";
  } else {
    $s->bind_param('s', $_POST['username']);
    $s->execute();
    $s->bind_result($uid,$hash);
    if ($s->fetch() && password_verify($_POST['password'], $hash)) {
      $_SESSION = ['user_type'=>'patient','user_id'=>$uid];
      header("Location: dashboard.php"); exit;
    }
    $s->close();
    echo "<p class='error'>Invalid username or password.</p>";
  }
}
?>
<?php include 'includes/footer.php'; ?>

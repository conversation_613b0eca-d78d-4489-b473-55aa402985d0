<?php
// Migration script to update from old schema to new schema
require_once 'includes/data.php';

echo "<h2>MediCarePlus Schema Migration</h2>";
echo "<p>This script will migrate your database to the new schema structure.</p>";

// Check if this is a fresh install or migration
$tables_check = $conn->query("SHOW TABLES");
$existing_tables = [];
while ($row = $tables_check->fetch_array()) {
    $existing_tables[] = $row[0];
}

echo "<h3>Current Tables Found:</h3>";
echo "<ul>";
foreach ($existing_tables as $table) {
    echo "<li>$table</li>";
}
echo "</ul>";

// Check if we need to migrate or create fresh
$needs_migration = in_array('admin_logins', $existing_tables) || in_array('doctor_logins', $existing_tables);
$has_new_schema = in_array('admins', $existing_tables) && in_array('departments', $existing_tables);

if ($has_new_schema) {
    echo "<p style='color: green;'>✅ New schema already exists!</p>";
    
    // Just ensure admin account exists
    $admin_check = $conn->query("SELECT COUNT(*) as count FROM admins");
    if ($admin_check) {
        $admin_count = $admin_check->fetch_assoc()['count'];
        if ($admin_count == 0) {
            echo "<p style='color: orange;'>Creating default admin account...</p>";
            createDefaultAdmin($conn);
        } else {
            echo "<p style='color: green;'>✅ Admin accounts exist.</p>";
        }
    }
} else {
    echo "<p style='color: orange;'>⚠️ Running migration to new schema...</p>";
    
    if (isset($_POST['confirm_migration'])) {
        runMigration($conn, $needs_migration);
    } else {
        showMigrationForm($needs_migration);
    }
}

function createDefaultAdmin($conn) {
    $username = 'admin';
    $password = 'admin123';
    $password_hash = password_hash($password, PASSWORD_BCRYPT);
    
    $stmt = $conn->prepare("INSERT INTO admins (full_name, email, phone, username, password_hash, role) VALUES (?, ?, ?, ?, ?, ?)");
    $full_name = 'System Administrator';
    $email = '<EMAIL>';
    $phone = '+***********';
    $role = 'super_admin';
    
    $stmt->bind_param('ssssss', $full_name, $email, $phone, $username, $password_hash, $role);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ Default admin account created!</p>";
        echo "<p><strong>Username:</strong> admin</p>";
        echo "<p><strong>Password:</strong> admin123</p>";
    } else {
        echo "<p style='color: red;'>❌ Error creating admin: " . $stmt->error . "</p>";
    }
}

function showMigrationForm($needs_migration) {
    echo "<div style='background: #fff3cd; padding: 1rem; border-radius: 5px; margin: 1rem 0;'>";
    echo "<h3>⚠️ Migration Required</h3>";
    if ($needs_migration) {
        echo "<p>This will migrate your existing data to the new schema structure.</p>";
        echo "<p><strong>What will happen:</strong></p>";
        echo "<ul>";
        echo "<li>Backup existing admin_logins and doctor_logins data</li>";
        echo "<li>Create new schema with departments, enhanced tables</li>";
        echo "<li>Migrate existing data to new structure</li>";
        echo "<li>Create default accounts if needed</li>";
        echo "</ul>";
    } else {
        echo "<p>This will create the new database schema from scratch.</p>";
        echo "<p><strong>What will be created:</strong></p>";
        echo "<ul>";
        echo "<li>Departments table with sample departments</li>";
        echo "<li>Enhanced admins, doctors, patients tables</li>";
        echo "<li>Enhanced appointments table with status tracking</li>";
        echo "<li>New invoices table for billing</li>";
        echo "<li>Default admin and doctor accounts</li>";
        echo "</ul>";
    }
    echo "<form method='post'>";
    echo "<input type='hidden' name='confirm_migration' value='1'>";
    echo "<button type='submit' style='background: #007bff; color: white; padding: 1rem 2rem; border: none; border-radius: 5px; cursor: pointer;'>Proceed with Migration</button>";
    echo "</form>";
    echo "</div>";
}

function runMigration($conn, $needs_migration) {
    echo "<h3>Running Migration...</h3>";
    
    try {
        // Read and execute the new schema
        $schema_file = 'updated_database_schema.sql';
        if (!file_exists($schema_file)) {
            throw new Exception("Schema file not found: $schema_file");
        }
        
        $sql = file_get_contents($schema_file);
        
        // Split into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (empty($statement) || strpos($statement, '--') === 0) continue;
            
            if (!$conn->query($statement)) {
                echo "<p style='color: orange;'>Warning: " . $conn->error . "</p>";
                echo "<p>Statement: " . htmlspecialchars(substr($statement, 0, 100)) . "...</p>";
            }
        }
        
        echo "<p style='color: green;'>✅ Schema migration completed!</p>";
        
        // Verify the migration
        $admin_check = $conn->query("SELECT COUNT(*) as count FROM admins");
        if ($admin_check) {
            $admin_count = $admin_check->fetch_assoc()['count'];
            echo "<p style='color: green;'>✅ Found $admin_count admin account(s).</p>";
        }
        
        $dept_check = $conn->query("SELECT COUNT(*) as count FROM departments");
        if ($dept_check) {
            $dept_count = $dept_check->fetch_assoc()['count'];
            echo "<p style='color: green;'>✅ Found $dept_count department(s).</p>";
        }
        
        echo "<h3>Migration Complete!</h3>";
        echo "<p><strong>Default Login Credentials:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Admin:</strong> admin / admin123</li>";
        echo "<li><strong>Doctors:</strong> dr.sarah / doctor123, dr.michael / doctor123, etc.</li>";
        echo "</ul>";
        
        echo "<p><a href='admin_login.php' style='background: #28a745; color: white; padding: 0.5rem 1rem; text-decoration: none; border-radius: 5px;'>Go to Admin Login</a></p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Migration failed: " . $e->getMessage() . "</p>";
    }
}

echo "<hr>";
echo "<p><a href='admin_login.php'>← Back to Admin Login</a> | <a href='check_admin_setup.php'>Check Admin Setup</a></p>";
?>

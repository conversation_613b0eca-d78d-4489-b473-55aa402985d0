<?php
// Database structure checker and fixer
require_once 'includes/data.php';

echo "<h2>MediCarePlus Database Structure Checker</h2>";

// Check if database connection is working
if ($conn->connect_error) {
    die("<p style='color: red;'>Connection failed: " . $conn->connect_error . "</p>");
}

echo "<p style='color: green;'>✓ Database connection successful</p>";

// List of required tables and their expected columns
$required_tables = [
    'Patients' => ['PatientID', 'Name', 'DOB', 'Email', 'Phone', 'LoyaltyStatus'],
    'PatientLogins' => ['LoginID', 'PatientID', 'PasswordHash'],
    'Doctors' => ['DoctorID', 'Name', 'Specialization', 'Email', 'Phone'],
    'Appointments' => ['AppointmentID', 'PatientID', 'DoctorID', 'AppointmentDate', 'AppointmentTime', 'ConsultationType', 'Diagnosis'],
    'Products' => ['ProductID', 'Name', 'Description', 'Price', 'Stock', 'Category'],
    'Orders' => ['OrderID', 'CustomerID', 'ProductID', 'Quantity', 'OrderDate', 'DeliveryMethod', 'Status']
];

// Check existing tables
$result = $conn->query("SHOW TABLES");
$existing_tables = [];
if ($result) {
    while ($row = $result->fetch_array()) {
        $existing_tables[] = $row[0];
    }
}

echo "<h3>Table Status:</h3>";
$all_tables_ok = true;

foreach ($required_tables as $table_name => $required_columns) {
    echo "<h4>Table: $table_name</h4>";
    
    if (in_array($table_name, $existing_tables)) {
        echo "<p style='color: green;'>✓ Table exists</p>";
        
        // Check columns
        $result = $conn->query("DESCRIBE $table_name");
        $existing_columns = [];
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $existing_columns[] = $row['Field'];
            }
        }
        
        echo "<p>Existing columns: " . implode(', ', $existing_columns) . "</p>";
        echo "<p>Required columns: " . implode(', ', $required_columns) . "</p>";
        
        $missing_columns = array_diff($required_columns, $existing_columns);
        if (empty($missing_columns)) {
            echo "<p style='color: green;'>✓ All required columns present</p>";
        } else {
            echo "<p style='color: red;'>✗ Missing columns: " . implode(', ', $missing_columns) . "</p>";
            $all_tables_ok = false;
        }
    } else {
        echo "<p style='color: red;'>✗ Table does not exist</p>";
        $all_tables_ok = false;
    }
    echo "<hr>";
}

if ($all_tables_ok) {
    echo "<h3 style='color: green;'>✓ Database structure is correct!</h3>";
    echo "<p><a href='index.php'>Go to application</a></p>";
} else {
    echo "<h3 style='color: red;'>✗ Database structure needs to be fixed</h3>";
    echo "<p><strong>To fix the database:</strong></p>";
    echo "<ol>";
    echo "<li>Open phpMyAdmin (usually at <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a>)</li>";
    echo "<li>Select the 'medicareplus' database</li>";
    echo "<li>Go to the 'SQL' tab</li>";
    echo "<li>Copy and paste the contents of <strong>fix_database.sql</strong> file</li>";
    echo "<li>Click 'Go' to execute the script</li>";
    echo "<li>Refresh this page to verify the fix</li>";
    echo "</ol>";
    
    echo "<p><strong>Alternative:</strong> Run this command in your terminal:</p>";
    echo "<code>mysql -u root -p medicareplus < fix_database.sql</code>";
}

// Check for sample data
if ($all_tables_ok) {
    echo "<h3>Sample Data Check:</h3>";
    
    $doctor_count = $conn->query("SELECT COUNT(*) as count FROM Doctors")->fetch_assoc()['count'];
    $product_count = $conn->query("SELECT COUNT(*) as count FROM Products")->fetch_assoc()['count'];
    
    echo "<p>Doctors: $doctor_count</p>";
    echo "<p>Products: $product_count</p>";
    
    if ($doctor_count == 0 || $product_count == 0) {
        echo "<p style='color: orange;'>⚠ Consider running the fix_database.sql script to add sample data</p>";
    }
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3, h4 { color: #333; }
code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
hr { margin: 10px 0; }
</style>

<?php
include 'includes/header.php';
if (!isset($_SESSION['user_id'])) {
  header("Location: login.php"); exit;
}

$id = $_SESSION['user_id'];
$sql = "
  SELECT a.appointment_date, a.consultation_type, d.full_name AS Doctor, d.specialty, a.diagnosis
  FROM appointments a
  JOIN doctors d ON a.doctor_id=d.doctor_id
  WHERE a.patient_id=?
  ORDER BY a.appointment_date DESC
";
$st = $conn->prepare($sql);
$st->bind_param('i',$id);
$st->execute();
$res = $st->get_result();
?>

<div style="max-width: 1000px; margin: 0 auto; padding: 2rem;">
  <h2 style="text-align: center; color: #2c5aa0; margin-bottom: 2rem;">📋 My Appointments</h2>

  <div style="text-align: center; margin-bottom: 2rem;">
    <a href="book_appointment.php" class="btn btn-primary">📅 Book New Appointment</a>
  </div>

  <?php if ($res->num_rows > 0): ?>
    <div style="display: grid; gap: 1.5rem;">
      <?php while($r=$res->fetch_assoc()):
        $appointment_date = new DateTime($r['appointment_date']);
        $is_upcoming = $appointment_date > new DateTime();
        $status_color = $is_upcoming ? '#28a745' : '#6c757d';
        $status_text = $is_upcoming ? 'Upcoming' : 'Completed';
      ?>
        <div style="background: white; border-radius: 10px; padding: 1.5rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-left: 4px solid <?= $status_color ?>;">
          <div style="display: grid; grid-template-columns: 1fr auto; gap: 1rem; align-items: start;">
            <div>
              <h3 style="color: #2c5aa0; margin-bottom: 0.5rem;">
                👨‍⚕️ Dr. <?= htmlspecialchars($r['Doctor']) ?>
              </h3>
              <p style="color: #666; margin-bottom: 0.5rem;">
                <strong>Specialty:</strong> <?= htmlspecialchars($r['specialty']) ?>
              </p>
              <p style="color: #666; margin-bottom: 0.5rem;">
                <strong>Consultation Type:</strong> <?= htmlspecialchars($r['consultation_type']) ?>
              </p>
              <p style="color: #666; margin-bottom: 0.5rem;">
                <strong>Date & Time:</strong> <?= $appointment_date->format('F j, Y \a\t g:i A') ?>
              </p>
              <?php if (!empty($r['diagnosis'])): ?>
                <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px; margin-top: 1rem;">
                  <strong style="color: #2c5aa0;">Diagnosis:</strong>
                  <p style="margin-top: 0.5rem;"><?= htmlspecialchars($r['diagnosis']) ?></p>
                </div>
              <?php endif; ?>
            </div>
            <div style="text-align: center;">
              <span style="background: <?= $status_color ?>; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem;">
                <?= $status_text ?>
              </span>
            </div>
          </div>
        </div>
      <?php endwhile; ?>
    </div>
  <?php else: ?>
    <div style="text-align: center; padding: 3rem; background: #f8f9fa; border-radius: 10px;">
      <div style="font-size: 4rem; margin-bottom: 1rem;">📅</div>
      <h3 style="color: #666; margin-bottom: 1rem;">No Appointments Yet</h3>
      <p style="color: #666; margin-bottom: 2rem;">You haven't booked any appointments yet. Start by scheduling your first consultation!</p>
      <a href="book_appointment.php" class="btn btn-primary">📅 Book Your First Appointment</a>
    </div>
  <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>

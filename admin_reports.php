<?php
include 'includes/header.php';

// Check if user is logged in as admin
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
  header("Location: admin_login.php");
  exit;
}

// Get system statistics
$stats = [];

// Total counts
$patient_count = $conn->query("SELECT COUNT(*) as count FROM patients")->fetch_assoc()['count'];
$doctor_count = $conn->query("SELECT COUNT(*) as count FROM doctors")->fetch_assoc()['count'];
$appointment_count = $conn->query("SELECT COUNT(*) as count FROM appointments")->fetch_assoc()['count'];

// Monthly appointment statistics
$monthly_appointments = $conn->query("
  SELECT 
    DATE_FORMAT(appointment_date, '%Y-%m') as month,
    COUNT(*) as count
  FROM appointments 
  WHERE appointment_date >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
  GROUP BY DATE_FORMAT(appointment_date, '%Y-%m')
  ORDER BY month DESC
");

// Doctor appointment statistics
$doctor_stats = $conn->query("
  SELECT 
    d.full_name,
    d.specialty,
    COUNT(a.appointment_id) as appointment_count
  FROM doctors d
  LEFT JOIN appointments a ON d.doctor_id = a.doctor_id
  GROUP BY d.doctor_id
  ORDER BY appointment_count DESC
");

// Recent activity
$recent_patients = $conn->query("
  SELECT full_name, email, created_at
  FROM patients 
  ORDER BY created_at DESC 
  LIMIT 5
");

$recent_appointments = $conn->query("
  SELECT p.full_name as patient_name, d.full_name as doctor_name, 
         a.appointment_date, a.consultation_type
  FROM appointments a
  JOIN patients p ON a.patient_id = p.patient_id
  JOIN doctors d ON a.doctor_id = d.doctor_id
  ORDER BY a.created_at DESC
  LIMIT 5
");
?>

<div class="dashboard">
  <h2>📊 System Reports</h2>
  <p style="color: #666; margin-bottom: 2rem;">
    Overview of system statistics and activity
  </p>

  <!-- Summary Statistics -->
  <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 10px; margin-bottom: 2rem;">
    <h3>System Summary</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">👥</div>
        <strong><?= $patient_count ?></strong><br>
        <small>Total Patients</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">👨‍⚕️</div>
        <strong><?= $doctor_count ?></strong><br>
        <small>Total Doctors</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">📅</div>
        <strong><?= $appointment_count ?></strong><br>
        <small>Total Appointments</small>
      </div>
    </div>
  </div>

  <!-- Monthly Appointments -->
  <div style="margin-bottom: 2rem;">
    <h3>Monthly Appointments (Last 6 Months)</h3>
    <?php if ($monthly_appointments && $monthly_appointments->num_rows > 0): ?>
      <table>
        <thead>
          <tr>
            <th>Month</th>
            <th>Appointments</th>
          </tr>
        </thead>
        <tbody>
          <?php while($month = $monthly_appointments->fetch_assoc()): ?>
            <tr>
              <td><?= date('F Y', strtotime($month['month'] . '-01')) ?></td>
              <td><?= $month['count'] ?></td>
            </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p style="text-align: center; color: #666; padding: 2rem;">No appointment data available.</p>
    <?php endif; ?>
  </div>

  <!-- Doctor Statistics -->
  <div style="margin-bottom: 2rem;">
    <h3>Doctor Performance</h3>
    <?php if ($doctor_stats && $doctor_stats->num_rows > 0): ?>
      <table>
        <thead>
          <tr>
            <th>Doctor</th>
            <th>Specialty</th>
            <th>Total Appointments</th>
          </tr>
        </thead>
        <tbody>
          <?php while($doctor = $doctor_stats->fetch_assoc()): ?>
            <tr>
              <td><?= htmlspecialchars($doctor['full_name']) ?></td>
              <td><?= htmlspecialchars($doctor['specialty']) ?></td>
              <td><?= $doctor['appointment_count'] ?></td>
            </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p style="text-align: center; color: #666; padding: 2rem;">No doctor data available.</p>
    <?php endif; ?>
  </div>

  <!-- Recent Activity -->
  <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
    <div>
      <h3>Recent Patient Registrations</h3>
      <?php if ($recent_patients && $recent_patients->num_rows > 0): ?>
        <table>
          <thead>
            <tr>
              <th>Name</th>
              <th>Email</th>
              <th>Registered</th>
            </tr>
          </thead>
          <tbody>
            <?php while($patient = $recent_patients->fetch_assoc()): ?>
              <tr>
                <td><?= htmlspecialchars($patient['full_name']) ?></td>
                <td><?= htmlspecialchars($patient['email']) ?></td>
                <td><?= date('M j, Y', strtotime($patient['created_at'])) ?></td>
              </tr>
            <?php endwhile; ?>
          </tbody>
        </table>
      <?php else: ?>
        <p style="text-align: center; color: #666; padding: 1rem;">No recent registrations.</p>
      <?php endif; ?>
    </div>

    <div>
      <h3>Recent Appointments</h3>
      <?php if ($recent_appointments && $recent_appointments->num_rows > 0): ?>
        <table>
          <thead>
            <tr>
              <th>Patient</th>
              <th>Doctor</th>
              <th>Date</th>
            </tr>
          </thead>
          <tbody>
            <?php while($apt = $recent_appointments->fetch_assoc()): ?>
              <tr>
                <td><?= htmlspecialchars($apt['patient_name']) ?></td>
                <td><?= htmlspecialchars($apt['doctor_name']) ?></td>
                <td><?= date('M j, Y', strtotime($apt['appointment_date'])) ?></td>
              </tr>
            <?php endwhile; ?>
          </tbody>
        </table>
      <?php else: ?>
        <p style="text-align: center; color: #666; padding: 1rem;">No recent appointments.</p>
      <?php endif; ?>
    </div>
  </div>

  <div style="text-align: center;">
    <a href="admin_dashboard.php" class="btn-primary">← Back to Dashboard</a>
  </div>
</div>

<?php include 'includes/footer.php'; ?>

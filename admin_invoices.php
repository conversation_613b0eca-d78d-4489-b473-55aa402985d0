<?php
include 'includes/header.php';

// Check if user is logged in as admin
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
  header("Location: admin_login.php");
  exit;
}

// Handle invoice actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    switch ($_POST['action']) {
      case 'add':
        $stmt = $conn->prepare("INSERT INTO invoices (appointment_id, patient_id, amount, payment_status, payment_method, notes) VALUES (?, ?, ?, ?, ?, ?)");
        $appointment_id = !empty($_POST['appointment_id']) ? $_POST['appointment_id'] : null;
        $stmt->bind_param('iidsss', $appointment_id, $_POST['patient_id'], $_POST['amount'], $_POST['payment_status'], $_POST['payment_method'], $_POST['notes']);
        if ($stmt->execute()) {
          echo "<div class='success'>Invoice created successfully!</div>";
        } else {
          echo "<div class='error'>Error creating invoice: " . $stmt->error . "</div>";
        }
        break;
        
      case 'update_payment':
        $stmt = $conn->prepare("UPDATE invoices SET payment_status = ?, payment_method = ?, notes = ? WHERE invoice_id = ?");
        $stmt->bind_param('sssi', $_POST['payment_status'], $_POST['payment_method'], $_POST['notes'], $_POST['invoice_id']);
        if ($stmt->execute()) {
          echo "<div class='success'>Payment status updated successfully!</div>";
        } else {
          echo "<div class='error'>Error updating payment: " . $stmt->error . "</div>";
        }
        break;
        
      case 'delete':
        $stmt = $conn->prepare("DELETE FROM invoices WHERE invoice_id = ?");
        $stmt->bind_param('i', $_POST['invoice_id']);
        if ($stmt->execute()) {
          echo "<div class='success'>Invoice deleted successfully!</div>";
        } else {
          echo "<div class='error'>Error deleting invoice: " . $stmt->error . "</div>";
        }
        break;
    }
  }
}

// Get filter parameters
$filter_status = $_GET['filter_status'] ?? 'all';
$filter_patient = $_GET['filter_patient'] ?? '';

// Build query based on filters
$where_conditions = [];
$params = [];
$param_types = '';

if ($filter_status !== 'all') {
  $where_conditions[] = "i.payment_status = ?";
  $params[] = $filter_status;
  $param_types .= 's';
}

if (!empty($filter_patient)) {
  $where_conditions[] = "p.full_name LIKE ?";
  $params[] = "%$filter_patient%";
  $param_types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get all invoices with patient and appointment info
$query = "
  SELECT i.invoice_id, i.appointment_id, i.amount, i.payment_status, i.payment_method, 
         i.invoice_date, i.notes, p.full_name as patient_name, p.email as patient_email,
         a.appointment_date, d.full_name as doctor_name
  FROM invoices i
  JOIN patients p ON i.patient_id = p.patient_id
  LEFT JOIN appointments a ON i.appointment_id = a.appointment_id
  LEFT JOIN doctors d ON a.doctor_id = d.doctor_id
  $where_clause
  ORDER BY i.invoice_date DESC
";

if (!empty($params)) {
  $stmt = $conn->prepare($query);
  $stmt->bind_param($param_types, ...$params);
  $stmt->execute();
  $invoices = $stmt->get_result();
} else {
  $invoices = $conn->query($query);
}

// Get patients for dropdown
$patients = $conn->query("SELECT patient_id, full_name FROM patients ORDER BY full_name");

// Get appointments for dropdown (only completed appointments without invoices)
$appointments = $conn->query("
  SELECT a.appointment_id, p.full_name as patient_name, d.full_name as doctor_name, 
         a.appointment_date, a.consultation_type
  FROM appointments a
  JOIN patients p ON a.patient_id = p.patient_id
  JOIN doctors d ON a.doctor_id = d.doctor_id
  LEFT JOIN invoices i ON a.appointment_id = i.appointment_id
  WHERE a.status = 'Completed' AND i.invoice_id IS NULL
  ORDER BY a.appointment_date DESC
");

// Get invoice for editing if edit_id is set
$edit_invoice = null;
if (isset($_GET['edit_id'])) {
  $edit_stmt = $conn->prepare("SELECT * FROM invoices WHERE invoice_id = ?");
  $edit_stmt->bind_param('i', $_GET['edit_id']);
  $edit_stmt->execute();
  $edit_invoice = $edit_stmt->get_result()->fetch_assoc();
}
?>

<div class="dashboard">
  <h2>💰 Invoice Management</h2>
  <p style="color: #666; margin-bottom: 2rem;">
    Manage patient invoices and payment tracking
  </p>

  <!-- Filters -->
  <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 10px; margin-bottom: 2rem;">
    <h3>Filter Invoices</h3>
    <form method="get" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Payment Status</label>
        <select name="filter_status">
          <option value="all" <?= $filter_status === 'all' ? 'selected' : '' ?>>All Statuses</option>
          <option value="Pending" <?= $filter_status === 'Pending' ? 'selected' : '' ?>>Pending</option>
          <option value="Paid" <?= $filter_status === 'Paid' ? 'selected' : '' ?>>Paid</option>
          <option value="Overdue" <?= $filter_status === 'Overdue' ? 'selected' : '' ?>>Overdue</option>
        </select>
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Patient Name</label>
        <input type="text" name="filter_patient" value="<?= htmlspecialchars($filter_patient) ?>" placeholder="Search patient name">
      </div>
      
      <div>
        <button type="submit" class="btn-primary">Apply Filters</button>
        <a href="admin_invoices.php" style="background: #6c757d; color: white; padding: 0.75rem 1rem; border-radius: 5px; text-decoration: none; margin-left: 0.5rem;">Clear</a>
      </div>
    </form>
  </div>

  <!-- Add New Invoice Form -->
  <div style="background: #f8f9fa; padding: 2rem; border-radius: 10px; margin-bottom: 2rem;">
    <h3>Create New Invoice</h3>
    <form method="post" action="" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
      <input type="hidden" name="action" value="add">
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Appointment (Optional)</label>
        <select name="appointment_id">
          <option value="">Select Appointment</option>
          <?php while($apt = $appointments->fetch_assoc()): ?>
            <option value="<?= $apt['appointment_id'] ?>">
              <?= htmlspecialchars($apt['patient_name']) ?> - <?= htmlspecialchars($apt['doctor_name']) ?> 
              (<?= date('M j, Y', strtotime($apt['appointment_date'])) ?>)
            </option>
          <?php endwhile; ?>
        </select>
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Patient</label>
        <select name="patient_id" required>
          <option value="">Select Patient</option>
          <?php 
          $patients->data_seek(0); // Reset pointer
          while($patient = $patients->fetch_assoc()): ?>
            <option value="<?= $patient['patient_id'] ?>"><?= htmlspecialchars($patient['full_name']) ?></option>
          <?php endwhile; ?>
        </select>
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Amount (LKR)</label>
        <input type="number" name="amount" step="0.01" min="0" required placeholder="0.00">
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Payment Status</label>
        <select name="payment_status" required>
          <option value="Pending">Pending</option>
          <option value="Paid">Paid</option>
          <option value="Overdue">Overdue</option>
        </select>
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Payment Method</label>
        <select name="payment_method">
          <option value="">Select Method</option>
          <option value="Cash">Cash</option>
          <option value="Card">Card</option>
          <option value="Bank Transfer">Bank Transfer</option>
          <option value="Insurance">Insurance</option>
        </select>
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Notes</label>
        <input type="text" name="notes" placeholder="Additional notes">
      </div>
      
      <button type="submit" class="btn-primary">Create Invoice</button>
    </form>
  </div>

  <!-- Invoices List -->
  <div>
    <h3>All Invoices</h3>
    <?php if ($invoices && $invoices->num_rows > 0): ?>
      <table>
        <thead>
          <tr>
            <th>Invoice #</th>
            <th>Patient</th>
            <th>Doctor/Date</th>
            <th>Amount</th>
            <th>Status</th>
            <th>Payment Method</th>
            <th>Invoice Date</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <?php while($invoice = $invoices->fetch_assoc()): ?>
            <tr>
              <td><strong>#<?= str_pad($invoice['invoice_id'], 4, '0', STR_PAD_LEFT) ?></strong></td>
              <td>
                <?= htmlspecialchars($invoice['patient_name']) ?><br>
                <small><?= htmlspecialchars($invoice['patient_email']) ?></small>
              </td>
              <td>
                <?php if ($invoice['doctor_name']): ?>
                  <?= htmlspecialchars($invoice['doctor_name']) ?><br>
                  <small><?= date('M j, Y', strtotime($invoice['appointment_date'])) ?></small>
                <?php else: ?>
                  <span style="color: #666;">Direct Invoice</span>
                <?php endif; ?>
              </td>
              <td><strong>LKR <?= number_format($invoice['amount'], 2) ?></strong></td>
              <td>
                <?php
                $status_colors = [
                  'Paid' => 'background: #28a745; color: white;',
                  'Pending' => 'background: #ffc107; color: black;',
                  'Overdue' => 'background: #dc3545; color: white;'
                ];
                $status_color = $status_colors[$invoice['payment_status']] ?? 'background: #6c757d; color: white;';
                ?>
                <span style="padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem; <?= $status_color ?>">
                  <?= htmlspecialchars($invoice['payment_status']) ?>
                </span>
              </td>
              <td><?= htmlspecialchars($invoice['payment_method'] ?? 'Not specified') ?></td>
              <td><?= date('M j, Y', strtotime($invoice['invoice_date'])) ?></td>
              <td>
                <a href="?edit_id=<?= $invoice['invoice_id'] ?>" style="background: #007bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; text-decoration: none; margin-right: 0.5rem;">Edit</a>
                <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this invoice?')">
                  <input type="hidden" name="action" value="delete">
                  <input type="hidden" name="invoice_id" value="<?= $invoice['invoice_id'] ?>">
                  <button type="submit" style="background: #dc3545; color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 4px; cursor: pointer;">Delete</button>
                </form>
              </td>
            </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p style="text-align: center; color: #666; padding: 2rem;">No invoices found.</p>
    <?php endif; ?>
  </div>

  <div style="margin-top: 2rem; text-align: center;">
    <a href="admin_dashboard.php" class="btn-primary">← Back to Dashboard</a>
  </div>
</div>

<?php include 'includes/footer.php'; ?>

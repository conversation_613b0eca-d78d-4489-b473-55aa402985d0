<?php
// Check admin setup and create default admin if needed
require_once 'includes/data.php';

echo "<h2>Admin Setup Checker</h2>";

// Check if admins table exists
$table_check = $conn->query("SHOW TABLES LIKE 'admins'");
if ($table_check->num_rows == 0) {
    echo "<p style='color: red;'>❌ Admins table does not exist.</p>";
    echo "<p><strong>Solution:</strong> Run the updated_database_schema.sql file first.</p>";
    exit;
}

echo "<p style='color: green;'>✅ Admins table exists.</p>";

// Check if any admin accounts exist
$admin_check = $conn->query("SELECT COUNT(*) as count FROM admins");
$admin_count = $admin_check->fetch_assoc()['count'];

if ($admin_count == 0) {
    echo "<p style='color: orange;'>⚠️ No admin accounts found. Creating default admin...</p>";
    
    // Create default admin account
    $username = 'admin';
    $password = 'admin123';
    $password_hash = password_hash($password, PASSWORD_BCRYPT);
    
    $stmt = $conn->prepare("INSERT INTO admins (full_name, email, phone, username, password_hash, role) VALUES (?, ?, ?, ?, ?, ?)");
    $full_name = 'System Administrator';
    $email = '<EMAIL>';
    $phone = '+***********';
    $role = 'super_admin';
    
    $stmt->bind_param('ssssss', $full_name, $email, $phone, $username, $password_hash, $role);
    
    if ($stmt->execute()) {
        echo "<p style='color: green;'>✅ Default admin account created successfully!</p>";
        echo "<p><strong>Username:</strong> admin</p>";
        echo "<p><strong>Password:</strong> admin123</p>";
    } else {
        echo "<p style='color: red;'>❌ Error creating admin account: " . $stmt->error . "</p>";
    }
} else {
    echo "<p style='color: green;'>✅ Found $admin_count admin account(s).</p>";
    
    // Show existing admin accounts
    $admins = $conn->query("SELECT username, full_name, email, role FROM admins");
    echo "<h3>Existing Admin Accounts:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Username</th><th>Full Name</th><th>Email</th><th>Role</th></tr>";
    while ($admin = $admins->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($admin['username']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['full_name']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['email']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['role']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p><strong>Note:</strong> If you forgot the password, you can reset it by running this script again or manually updating the database.</p>";
}

// Test password verification
if (isset($_POST['test_login'])) {
    $test_username = $_POST['test_username'];
    $test_password = $_POST['test_password'];
    
    $stmt = $conn->prepare("SELECT admin_id, password_hash, full_name FROM admins WHERE username = ?");
    $stmt->bind_param('s', $test_username);
    $stmt->execute();
    $stmt->bind_result($admin_id, $password_hash, $full_name);
    
    if ($stmt->fetch() && password_verify($test_password, $password_hash)) {
        echo "<p style='color: green;'>✅ Login test successful for user: " . htmlspecialchars($full_name) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Login test failed. Check username and password.</p>";
    }
    $stmt->close();
}

// Add option to reset admin password
if (isset($_POST['reset_password'])) {
    $reset_username = $_POST['reset_username'];
    $new_password = $_POST['new_password'];
    $new_password_hash = password_hash($new_password, PASSWORD_BCRYPT);
    
    $stmt = $conn->prepare("UPDATE admins SET password_hash = ? WHERE username = ?");
    $stmt->bind_param('ss', $new_password_hash, $reset_username);
    
    if ($stmt->execute() && $stmt->affected_rows > 0) {
        echo "<p style='color: green;'>✅ Password reset successful for user: " . htmlspecialchars($reset_username) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Password reset failed. User not found or error occurred.</p>";
    }
}
?>

<h3>Test Admin Login</h3>
<form method="post" style="background: #f0f0f0; padding: 1rem; margin: 1rem 0;">
    <input type="hidden" name="test_login" value="1">
    <p>
        <label>Username:</label><br>
        <input type="text" name="test_username" value="admin" required>
    </p>
    <p>
        <label>Password:</label><br>
        <input type="password" name="test_password" value="admin123" required>
    </p>
    <button type="submit">Test Login</button>
</form>

<h3>Reset Admin Password</h3>
<form method="post" style="background: #f0f0f0; padding: 1rem; margin: 1rem 0;">
    <input type="hidden" name="reset_password" value="1">
    <p>
        <label>Username:</label><br>
        <input type="text" name="reset_username" value="admin" required>
    </p>
    <p>
        <label>New Password:</label><br>
        <input type="password" name="new_password" value="admin123" required>
    </p>
    <button type="submit">Reset Password</button>
</form>

<p><a href="admin_login.php">← Back to Admin Login</a></p>

<?php
include 'includes/header.php';

// Check if user is logged in as admin
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
  header("Location: admin_login.php");
  exit;
}

// Handle patient actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    switch ($_POST['action']) {
      case 'add':
        $stmt = $conn->prepare("INSERT INTO patients (full_name, dob, email, phone) VALUES (?, ?, ?, ?)");
        $stmt->bind_param('ssss', $_POST['full_name'], $_POST['dob'], $_POST['email'], $_POST['phone']);
        if ($stmt->execute()) {
          $patient_id = $stmt->insert_id;
          
          // Create login account for the new patient if username and password provided
          if (!empty($_POST['username']) && !empty($_POST['password'])) {
            $password_hash = password_hash($_POST['password'], PASSWORD_BCRYPT);
            $login_stmt = $conn->prepare("INSERT INTO patient_logins (patient_id, username, password_hash) VALUES (?, ?, ?)");
            $login_stmt->bind_param('iss', $patient_id, $_POST['username'], $password_hash);
            if ($login_stmt->execute()) {
              echo "<div class='success'>Patient added successfully with login credentials!</div>";
            } else {
              echo "<div class='success'>Patient added successfully, but login creation failed: " . $login_stmt->error . "</div>";
            }
          } else {
            echo "<div class='success'>Patient added successfully!</div>";
          }
        } else {
          echo "<div class='error'>Error adding patient: " . $stmt->error . "</div>";
        }
        break;
        
      case 'edit':
        $stmt = $conn->prepare("UPDATE patients SET full_name = ?, dob = ?, email = ?, phone = ? WHERE patient_id = ?");
        $stmt->bind_param('ssssi', $_POST['full_name'], $_POST['dob'], $_POST['email'], $_POST['phone'], $_POST['patient_id']);
        if ($stmt->execute()) {
          echo "<div class='success'>Patient updated successfully!</div>";
        } else {
          echo "<div class='error'>Error updating patient: " . $stmt->error . "</div>";
        }
        break;
        
      case 'delete':
        // First delete from patient_logins
        $stmt1 = $conn->prepare("DELETE FROM patient_logins WHERE patient_id = ?");
        $stmt1->bind_param('i', $_POST['patient_id']);
        $stmt1->execute();
        
        // Then delete from patients
        $stmt2 = $conn->prepare("DELETE FROM patients WHERE patient_id = ?");
        $stmt2->bind_param('i', $_POST['patient_id']);
        if ($stmt2->execute()) {
          echo "<div class='success'>Patient deleted successfully!</div>";
        } else {
          echo "<div class='error'>Error deleting patient: " . $stmt2->error . "</div>";
        }
        break;
    }
  }
}

// Get all patients with their login info
$patients = $conn->query("
  SELECT p.patient_id, p.full_name, p.dob, p.email, p.phone, pl.username,
         COUNT(a.appointment_id) as appointment_count
  FROM patients p
  LEFT JOIN patient_logins pl ON p.patient_id = pl.patient_id
  LEFT JOIN appointments a ON p.patient_id = a.patient_id
  GROUP BY p.patient_id
  ORDER BY p.full_name
");

// Get patient for editing if edit_id is set
$edit_patient = null;
if (isset($_GET['edit_id'])) {
  $edit_stmt = $conn->prepare("SELECT * FROM patients WHERE patient_id = ?");
  $edit_stmt->bind_param('i', $_GET['edit_id']);
  $edit_stmt->execute();
  $edit_patient = $edit_stmt->get_result()->fetch_assoc();
}
?>

<div class="dashboard">
  <h2>👥 Patient Management</h2>
  <p style="color: #666; margin-bottom: 2rem;">
    Manage all patients in the system
  </p>

  <!-- Add/Edit Patient Form -->
  <div style="background: #f8f9fa; padding: 2rem; border-radius: 10px; margin-bottom: 2rem;">
    <h3><?= $edit_patient ? 'Edit Patient' : 'Add New Patient' ?></h3>
    <form method="post" action="" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; align-items: end;">
      <input type="hidden" name="action" value="<?= $edit_patient ? 'edit' : 'add' ?>">
      <?php if ($edit_patient): ?>
        <input type="hidden" name="patient_id" value="<?= $edit_patient['patient_id'] ?>">
      <?php endif; ?>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Full Name</label>
        <input type="text" name="full_name" required value="<?= $edit_patient ? htmlspecialchars($edit_patient['full_name']) : '' ?>">
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Date of Birth</label>
        <input type="date" name="dob" required value="<?= $edit_patient ? $edit_patient['dob'] : '' ?>">
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Email</label>
        <input type="email" name="email" required value="<?= $edit_patient ? htmlspecialchars($edit_patient['email']) : '' ?>">
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Phone</label>
        <input type="tel" name="phone" required value="<?= $edit_patient ? htmlspecialchars($edit_patient['phone']) : '' ?>">
      </div>
      
      <?php if (!$edit_patient): ?>
        <div>
          <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Username (Optional)</label>
          <input type="text" name="username" placeholder="For login access">
        </div>
        
        <div>
          <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Password (Optional)</label>
          <input type="password" name="password" placeholder="For login access">
        </div>
      <?php endif; ?>
      
      <div style="display: flex; gap: 0.5rem;">
        <button type="submit" class="btn-primary"><?= $edit_patient ? 'Update Patient' : 'Add Patient' ?></button>
        <?php if ($edit_patient): ?>
          <a href="admin_patients.php" style="background: #6c757d; color: white; padding: 0.75rem 1rem; border-radius: 5px; text-decoration: none;">Cancel</a>
        <?php endif; ?>
      </div>
    </form>
  </div>

  <!-- Patients List -->
  <div>
    <h3>All Patients</h3>
    <?php if ($patients && $patients->num_rows > 0): ?>
      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>Name</th>
            <th>Date of Birth</th>
            <th>Email</th>
            <th>Phone</th>
            <th>Username</th>
            <th>Appointments</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <?php while($patient = $patients->fetch_assoc()): ?>
            <tr>
              <td><?= $patient['patient_id'] ?></td>
              <td><?= htmlspecialchars($patient['full_name']) ?></td>
              <td><?= date('M j, Y', strtotime($patient['dob'])) ?></td>
              <td><?= htmlspecialchars($patient['email']) ?></td>
              <td><?= htmlspecialchars($patient['phone']) ?></td>
              <td><?= htmlspecialchars($patient['username'] ?? 'No login') ?></td>
              <td><?= $patient['appointment_count'] ?></td>
              <td>
                <a href="?edit_id=<?= $patient['patient_id'] ?>" style="background: #007bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; text-decoration: none; margin-right: 0.5rem;">Edit</a>
                <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this patient? This will also delete all their appointments and login data.')">
                  <input type="hidden" name="action" value="delete">
                  <input type="hidden" name="patient_id" value="<?= $patient['patient_id'] ?>">
                  <button type="submit" style="background: #dc3545; color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 4px; cursor: pointer;">Delete</button>
                </form>
              </td>
            </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p style="text-align: center; color: #666; padding: 2rem;">No patients found.</p>
    <?php endif; ?>
  </div>

  <div style="margin-top: 2rem; text-align: center;">
    <a href="admin_dashboard.php" class="btn-primary">← Back to Dashboard</a>
  </div>
</div>

<?php include 'includes/footer.php'; ?>

-- Fix MediCarePlus Database Schema
-- This script will drop existing tables and recreate them with correct structure

-- Drop existing tables if they exist (in correct order to handle foreign keys)
DROP TABLE IF EXISTS Orders;
DROP TABLE IF EXISTS Appointments;
DROP TABLE IF EXISTS PatientLogins;
DROP TABLE IF EXISTS Products;
DROP TABLE IF EXISTS Doctors;
DROP TABLE IF EXISTS Patients;

-- Recreate all tables with correct structure

-- Patients table
CREATE TABLE Patients (
    PatientID INT AUTO_INCREMENT PRIMARY KEY,
    Name VARCHAR(100) NOT NULL,
    DOB DATE NOT NULL,
    Email VARCHAR(100) UNIQUE NOT NULL,
    Phone VARCHAR(20) NOT NULL,
    LoyaltyStatus ENUM('New', 'Loyal') DEFAULT 'New',
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Patient logins table
CREATE TABLE PatientLogins (
    LoginID INT AUTO_INCREMENT PRIMARY KEY,
    Pat<PERSON>ID INT NOT NULL,
    PasswordHash VARCHAR(255) NOT NULL,
    <PERSON>At TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (<PERSON>ientID) REFERENCES Patients(PatientID) ON DELETE CASCADE
);

-- Doctors table
CREATE TABLE Doctors (
    DoctorID INT AUTO_INCREMENT PRIMARY KEY,
    Name VARCHAR(100) NOT NULL,
    Specialization VARCHAR(100),
    Email VARCHAR(100) UNIQUE,
    Phone VARCHAR(20),
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Appointments table
CREATE TABLE Appointments (
    AppointmentID INT AUTO_INCREMENT PRIMARY KEY,
    PatientID INT NOT NULL,
    DoctorID INT NOT NULL,
    AppointmentDate DATE NOT NULL,
    AppointmentTime TIME NOT NULL,
    ConsultationType VARCHAR(100),
    Diagnosis TEXT,
    Status ENUM('Scheduled', 'Completed', 'Cancelled') DEFAULT 'Scheduled',
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (PatientID) REFERENCES Patients(PatientID) ON DELETE CASCADE,
    FOREIGN KEY (DoctorID) REFERENCES Doctors(DoctorID) ON DELETE CASCADE
);

-- Products table
CREATE TABLE Products (
    ProductID INT AUTO_INCREMENT PRIMARY KEY,
    Name VARCHAR(100) NOT NULL,
    Description TEXT,
    Price DECIMAL(10,2) NOT NULL,
    Stock INT DEFAULT 0,
    Category VARCHAR(50),
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Orders table
CREATE TABLE Orders (
    OrderID INT AUTO_INCREMENT PRIMARY KEY,
    CustomerID INT NOT NULL,
    ProductID INT NOT NULL,
    Quantity INT NOT NULL,
    OrderDate DATE NOT NULL,
    DeliveryMethod ENUM('Pickup', 'Delivery') DEFAULT 'Pickup',
    Status ENUM('Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled') DEFAULT 'Pending',
    TotalAmount DECIMAL(10,2),
    CreatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (CustomerID) REFERENCES Patients(PatientID) ON DELETE CASCADE,
    FOREIGN KEY (ProductID) REFERENCES Products(ProductID) ON DELETE CASCADE
);

-- Insert sample doctors
INSERT INTO Doctors (Name, Specialization, Email, Phone) VALUES
('Dr. Sarah Johnson', 'General Medicine', '<EMAIL>', '+94771234567'),
('Dr. Michael Chen', 'Cardiology', '<EMAIL>', '+94771234568'),
('Dr. Priya Patel', 'Dermatology', '<EMAIL>', '+94771234569'),
('Dr. James Wilson', 'Pediatrics', '<EMAIL>', '+94771234570');

-- Insert sample products
INSERT INTO Products (Name, Description, Price, Stock, Category) VALUES
('Paracetamol 500mg', 'Pain relief and fever reducer', 150.00, 100, 'Medicine'),
('Vitamin C Tablets', 'Immune system support', 250.00, 50, 'Supplements'),
('Digital Thermometer', 'Accurate temperature measurement', 1200.00, 25, 'Medical Devices'),
('Hand Sanitizer 250ml', 'Antibacterial hand sanitizer', 300.00, 75, 'Hygiene'),
('Face Masks (Pack of 10)', 'Disposable surgical masks', 500.00, 40, 'Protection');

-- Show table structures for verification
SHOW TABLES;
DESCRIBE Patients;
DESCRIBE PatientLogins;
DESCRIBE Doctors;
DESCRIBE Appointments;
DESCRIBE Products;
DESCRIBE Orders;

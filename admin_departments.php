<?php
include 'includes/header.php';

// Check if user is logged in as admin
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'admin') {
  header("Location: admin_login.php");
  exit;
}

// Handle department actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    switch ($_POST['action']) {
      case 'add':
        $stmt = $conn->prepare("INSERT INTO departments (department_name, description) VALUES (?, ?)");
        $stmt->bind_param('ss', $_POST['department_name'], $_POST['description']);
        if ($stmt->execute()) {
          echo "<div class='success'>Department added successfully!</div>";
        } else {
          if ($stmt->errno == 1062) { // Duplicate entry error
            echo "<div class='error'>Department name already exists. Please choose a different name.</div>";
          } else {
            echo "<div class='error'>Error adding department: " . $stmt->error . "</div>";
          }
        }
        break;
        
      case 'edit':
        $stmt = $conn->prepare("UPDATE departments SET department_name = ?, description = ? WHERE department_id = ?");
        $stmt->bind_param('ssi', $_POST['department_name'], $_POST['description'], $_POST['department_id']);
        if ($stmt->execute()) {
          echo "<div class='success'>Department updated successfully!</div>";
        } else {
          if ($stmt->errno == 1062) { // Duplicate entry error
            echo "<div class='error'>Department name already exists. Please choose a different name.</div>";
          } else {
            echo "<div class='error'>Error updating department: " . $stmt->error . "</div>";
          }
        }
        break;
        
      case 'delete':
        // Check if department has doctors
        $check_stmt = $conn->prepare("SELECT COUNT(*) as count FROM doctors WHERE department_id = ?");
        $check_stmt->bind_param('i', $_POST['department_id']);
        $check_stmt->execute();
        $result = $check_stmt->get_result();
        $doctor_count = $result->fetch_assoc()['count'];
        
        if ($doctor_count > 0) {
          echo "<div class='error'>Cannot delete department. It has $doctor_count doctor(s) assigned. Please reassign or remove doctors first.</div>";
        } else {
          $stmt = $conn->prepare("DELETE FROM departments WHERE department_id = ?");
          $stmt->bind_param('i', $_POST['department_id']);
          if ($stmt->execute()) {
            echo "<div class='success'>Department deleted successfully!</div>";
          } else {
            echo "<div class='error'>Error deleting department: " . $stmt->error . "</div>";
          }
        }
        break;
    }
  }
}

// Get all departments with doctor count
$departments = $conn->query("
  SELECT d.department_id, d.department_name, d.description, d.created_at,
         COUNT(doc.doctor_id) as doctor_count
  FROM departments d
  LEFT JOIN doctors doc ON d.department_id = doc.department_id
  GROUP BY d.department_id
  ORDER BY d.department_name
");

// Get department for editing if edit_id is set
$edit_department = null;
if (isset($_GET['edit_id'])) {
  $edit_stmt = $conn->prepare("SELECT * FROM departments WHERE department_id = ?");
  $edit_stmt->bind_param('i', $_GET['edit_id']);
  $edit_stmt->execute();
  $edit_department = $edit_stmt->get_result()->fetch_assoc();
}
?>

<div class="dashboard">
  <h2>🏥 Department Management</h2>
  <p style="color: #666; margin-bottom: 2rem;">
    Manage hospital departments and specialties
  </p>

  <!-- Add/Edit Department Form -->
  <div style="background: #f8f9fa; padding: 2rem; border-radius: 10px; margin-bottom: 2rem;">
    <h3><?= $edit_department ? 'Edit Department' : 'Add New Department' ?></h3>
    <form method="post" action="" style="display: grid; grid-template-columns: 1fr 2fr auto; gap: 1rem; align-items: end;">
      <input type="hidden" name="action" value="<?= $edit_department ? 'edit' : 'add' ?>">
      <?php if ($edit_department): ?>
        <input type="hidden" name="department_id" value="<?= $edit_department['department_id'] ?>">
      <?php endif; ?>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Department Name</label>
        <input type="text" name="department_name" required value="<?= $edit_department ? htmlspecialchars($edit_department['department_name']) : '' ?>" placeholder="e.g., Cardiology">
      </div>
      
      <div>
        <label style="display: block; margin-bottom: 0.5rem; font-weight: 600;">Description</label>
        <textarea name="description" rows="3" placeholder="Brief description of the department"><?= $edit_department ? htmlspecialchars($edit_department['description']) : '' ?></textarea>
      </div>
      
      <div style="display: flex; gap: 0.5rem; flex-direction: column;">
        <button type="submit" class="btn-primary"><?= $edit_department ? 'Update' : 'Add' ?> Department</button>
        <?php if ($edit_department): ?>
          <a href="admin_departments.php" style="background: #6c757d; color: white; padding: 0.75rem 1rem; border-radius: 5px; text-decoration: none; text-align: center;">Cancel</a>
        <?php endif; ?>
      </div>
    </form>
  </div>

  <!-- Departments List -->
  <div>
    <h3>All Departments</h3>
    <?php if ($departments && $departments->num_rows > 0): ?>
      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>Department Name</th>
            <th>Description</th>
            <th>Doctors</th>
            <th>Created</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <?php while($dept = $departments->fetch_assoc()): ?>
            <tr>
              <td><?= $dept['department_id'] ?></td>
              <td>
                <strong><?= htmlspecialchars($dept['department_name']) ?></strong>
              </td>
              <td>
                <small><?= htmlspecialchars($dept['description']) ?></small>
              </td>
              <td>
                <span style="background: #007bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">
                  <?= $dept['doctor_count'] ?> doctor<?= $dept['doctor_count'] != 1 ? 's' : '' ?>
                </span>
              </td>
              <td><?= date('M j, Y', strtotime($dept['created_at'])) ?></td>
              <td>
                <a href="?edit_id=<?= $dept['department_id'] ?>" style="background: #007bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; text-decoration: none; margin-right: 0.5rem;">Edit</a>
                <?php if ($dept['doctor_count'] == 0): ?>
                  <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this department?')">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="department_id" value="<?= $dept['department_id'] ?>">
                    <button type="submit" style="background: #dc3545; color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 4px; cursor: pointer;">Delete</button>
                  </form>
                <?php else: ?>
                  <span style="background: #6c757d; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem;">Has Doctors</span>
                <?php endif; ?>
              </td>
            </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p style="text-align: center; color: #666; padding: 2rem;">No departments found.</p>
    <?php endif; ?>
  </div>

  <!-- Quick Stats -->
  <div style="margin-top: 3rem; background: #f8f9fa; padding: 1.5rem; border-radius: 10px;">
    <h3>Department Statistics</h3>
    <?php
    $total_departments = $departments->num_rows;
    $departments->data_seek(0); // Reset pointer
    $total_doctors = 0;
    $departments_with_doctors = 0;
    while($dept = $departments->fetch_assoc()) {
      $total_doctors += $dept['doctor_count'];
      if ($dept['doctor_count'] > 0) $departments_with_doctors++;
    }
    ?>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">🏥</div>
        <strong><?= $total_departments ?></strong><br>
        <small>Total Departments</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">👨‍⚕️</div>
        <strong><?= $total_doctors ?></strong><br>
        <small>Total Doctors</small>
      </div>
      <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
        <div style="font-size: 2rem; color: #2c5aa0;">✅</div>
        <strong><?= $departments_with_doctors ?></strong><br>
        <small>Active Departments</small>
      </div>
    </div>
  </div>

  <div style="margin-top: 2rem; text-align: center;">
    <a href="admin_dashboard.php" class="btn-primary">← Back to Dashboard</a>
  </div>
</div>

<?php include 'includes/footer.php'; ?>
